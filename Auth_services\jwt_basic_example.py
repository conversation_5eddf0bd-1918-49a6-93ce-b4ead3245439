from datetime import datetime, timedelta, timezone
from typing import Optional
from jose import JWTError, jwt

# <PERSON><PERSON><PERSON> hình
SECRET_KEY = "your-super-secret-key-minimum-32-characters-long"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(user_id: int, username: str) -> str:
    """Tạo JWT token"""
    now = datetime.now(timezone.utc)
    expire = now + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    payload = {
        "sub": str(user_id), 
            "username": username, 
            "iat": int(now.timestamp()),
            "exp": int(expire.timestamp())
    }
    
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    return token

def verify_token(token: str) -> Optional[dict]:
    """Verify JWT token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # <PERSON><PERSON><PERSON> tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.now(timezone.utc):
            return None
            
        return payload
        
    except JWTError:
        return None
    except Exception:
        return None

# Test functions
if __name__ == "__main__":
    # Tạo token
    token = create_access_token(user_id=123, username="test_user")
    print(f"Token được tạo: {token}")
    
    # Verify token
    payload = verify_token(token)
    if payload:
        print(f"Token hợp lệ! User ID: {payload['sub']}, Username: {payload['username']}")
    else:
        print("Token không hợp lệ!")
