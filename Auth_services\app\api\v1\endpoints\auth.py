from fastapi import APIRouter, Depends 
from ....controllers.auth_controllers import AuthController
from ....schemas.auth import RegisterRequest, UserResponse, LoginRequest

router = APIRouter()

@router.post("/register", response_model= UserResponse)
def register(
    payload: RegisterRequest, 
    controller: AuthController = Depends()
): 
    return controller.register(payload= payload)

@router.post("/login", response_model= UserResponse)
def login(
    payload: LoginRequest, 
    controller: AuthController = Depends()
):
    return controller.login(payload= payload)