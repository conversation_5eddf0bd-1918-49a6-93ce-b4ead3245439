# 🚫 Logout + Token Blacklist - Thu hồi tokens

## 📋 Mục tiêu
- ✅ Hiểu vấn đề với JWT stateless
- ✅ Implement Token Blacklist với Redis
- ✅ Logout functionality
- ✅ Revoke tokens trước khi hết hạn

---

## 🤔 Vấn đề với JWT Stateless

### JWT Stateless Problem:

**Vấn đề:**
- JWT là **stateless** → Server không lưu trữ token state
- Khi user logout → Token vẫn hợp lệ đến khi hết hạn
- Nếu token bị đánh cắp → Không thể "thu hồi" token

**Ví dụ scenario:**
```
1. User login → Nhận JWT token (expires in 30 minutes)
2. User logout sau 5 minutes
3. Token vẫn valid trong 25 minutes tiếp theo!
4. Attacker có thể sử dụng token trong 25 minutes này
```

### Giải pháp Token Blacklist:

**Concept:**
- <PERSON><PERSON><PERSON> danh sách tokens bị "blacklist" (thu hồi)
- Mỗi khi verify token → Ki<PERSON><PERSON> tra blacklist trước
- Nếu token trong blacklist → Reject request

**Implementation:**
- <PERSON>ử dụng Redis (in-memory database) để lưu blacklist
- Token tự động xóa khỏi blacklist khi hết hạn (TTL)

---

## 🔧 Bước 1: Setup Redis Blacklist

### 1.1 Cài đặt Redis

**Windows:**
```bash
# Download Redis từ https://redis.io/download
# Hoặc dùng Docker
docker run -d -p 6379:6379 redis:alpine
```

**Linux/Mac:**
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis
```

**Python Redis client:**
```bash
pip install redis
```

### 1.2 Tạo file `app/core/blacklist_service.py`

```python
import redis
from typing import Optional, Set
from datetime import datetime, timedelta
import json
import os

class TokenBlacklistService:
    """
    Service quản lý token blacklist sử dụng Redis
    """
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        """
        Khởi tạo Redis connection
        
        Args:
            redis_url: Redis connection URL
        """
        try:
            self.redis_client = redis.from_url(
                redis_url,
                decode_responses=True,  # Tự động decode bytes thành string
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            self.redis_client.ping()
            print(f"✅ Connected to Redis: {redis_url}")
            
        except redis.ConnectionError as e:
            print(f"❌ Redis connection failed: {e}")
            print("🔄 Falling back to in-memory blacklist")
            self.redis_client = None
            self._memory_blacklist: Set[str] = set()
```
**Giải thích:**
- `redis.from_url()`: Tạo Redis client từ URL
- `decode_responses=True`: Tự động convert bytes thành string
- `ping()`: Test connection để đảm bảo Redis hoạt động
- Fallback to in-memory nếu Redis không available

```python
    def add_token_to_blacklist(self, token: str, expires_in: int, reason: str = "logout"):
        """
        Thêm token vào blacklist
        
        Args:
            token: JWT token string
            expires_in: Thời gian token còn sống (seconds)
            reason: Lý do blacklist (logout, security, etc.)
        """
        if self.redis_client:
            try:
                # Tạo key với prefix
                key = f"blacklist:{token}"
                
                # Lưu thông tin token với TTL
                token_info = {
                    "reason": reason,
                    "blacklisted_at": datetime.utcnow().isoformat(),
                    "original_expires_in": expires_in
                }
                
                # Set với TTL = thời gian token còn sống
                self.redis_client.setex(
                    key, 
                    expires_in, 
                    json.dumps(token_info)
                )
                
                print(f"✅ Token blacklisted in Redis (TTL: {expires_in}s, Reason: {reason})")
                return True
                
            except redis.RedisError as e:
                print(f"❌ Redis error: {e}")
                return False
        else:
            # Fallback to memory
            self._memory_blacklist.add(token)
            print(f"✅ Token blacklisted in memory (Reason: {reason})")
            return True
```
**Giải thích:**
- `f"blacklist:{token}"`: Prefix để phân biệt với data khác trong Redis
- `setex()`: Set key với expiration time (TTL)
- TTL = thời gian token còn sống → Token tự động xóa khỏi blacklist khi hết hạn
- Lưu metadata (reason, timestamp) để audit

```python
    def is_token_blacklisted(self, token: str) -> bool:
        """
        Kiểm tra token có trong blacklist không
        
        Args:
            token: JWT token string
        
        Returns:
            bool: True nếu token bị blacklist
        """
        if self.redis_client:
            try:
                key = f"blacklist:{token}"
                exists = self.redis_client.exists(key)
                
                if exists:
                    # Lấy thông tin blacklist để log
                    info = self.redis_client.get(key)
                    if info:
                        token_info = json.loads(info)
                        print(f"🚫 Token is blacklisted (Reason: {token_info.get('reason', 'unknown')})")
                    return True
                
                return False
                
            except redis.RedisError as e:
                print(f"❌ Redis error checking blacklist: {e}")
                # Nếu Redis lỗi, cho phép token (fail-open)
                return False
        else:
            # Check memory blacklist
            is_blacklisted = token in self._memory_blacklist
            if is_blacklisted:
                print("🚫 Token is blacklisted (in-memory)")
            return is_blacklisted
```
**Giải thích:**
- `exists()`: Kiểm tra key có tồn tại trong Redis không
- Lấy thông tin blacklist để log reason
- **Fail-open policy**: Nếu Redis lỗi → Cho phép token (tránh block toàn bộ service)

```python
    def remove_token_from_blacklist(self, token: str) -> bool:
        """
        Xóa token khỏi blacklist (nếu cần)
        
        Args:
            token: JWT token string
        
        Returns:
            bool: True nếu xóa thành công
        """
        if self.redis_client:
            try:
                key = f"blacklist:{token}"
                deleted = self.redis_client.delete(key)
                
                if deleted:
                    print(f"✅ Token removed from blacklist")
                    return True
                else:
                    print(f"⚠️ Token not found in blacklist")
                    return False
                    
            except redis.RedisError as e:
                print(f"❌ Redis error removing token: {e}")
                return False
        else:
            # Remove from memory
            if token in self._memory_blacklist:
                self._memory_blacklist.remove(token)
                print(f"✅ Token removed from memory blacklist")
                return True
            return False
    
    def get_blacklist_stats(self) -> dict:
        """
        Lấy thống kê blacklist
        
        Returns:
            dict: Thống kê blacklist
        """
        if self.redis_client:
            try:
                # Đếm số keys có prefix "blacklist:"
                keys = self.redis_client.keys("blacklist:*")
                return {
                    "total_blacklisted_tokens": len(keys),
                    "storage": "redis",
                    "redis_connected": True
                }
            except redis.RedisError:
                return {
                    "total_blacklisted_tokens": 0,
                    "storage": "redis",
                    "redis_connected": False
                }
        else:
            return {
                "total_blacklisted_tokens": len(self._memory_blacklist),
                "storage": "memory",
                "redis_connected": False
            }

# Tạo singleton instance
blacklist_service = TokenBlacklistService()
```

---

## 🔄 Bước 2: Cập nhật JWT Verification

### 2.1 Cập nhật `app/core/jwt_utils.py`

```python
from .blacklist_service import blacklist_service

def verify_access_token_with_blacklist(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Access Token với kiểm tra blacklist
    
    Args:
        token: JWT token string
    
    Returns:
        Dict: Token payload nếu hợp lệ, None nếu không hợp lệ hoặc bị blacklist
    """
    # Kiểm tra blacklist TRƯỚC khi decode token
    if blacklist_service.is_token_blacklisted(token):
        print("❌ Token is blacklisted")
        return None
```
**Giải thích:**
- Kiểm tra blacklist TRƯỚC khi decode token
- Tiết kiệm CPU: Không cần decode token đã bị blacklist
- Return None ngay lập tức nếu token bị blacklist

```python
    # Verify token như bình thường
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra loại token
        if payload.get("type") != "access":
            print("❌ Token is not an access token")
            return None
        
        # Kiểm tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            print("❌ Access token expired")
            return None
            
        print(f"✅ Access token verified for user: {payload.get('username')}")
        return payload
        
    except JWTError as e:
        print(f"❌ Access token verification failed: {e}")
        return None

def calculate_token_remaining_time(token: str) -> int:
    """
    Tính thời gian còn lại của token (để set TTL cho blacklist)
    
    Args:
        token: JWT token string
    
    Returns:
        int: Seconds còn lại, 0 nếu token không hợp lệ
    """
    try:
        # Decode token để lấy exp (không verify signature để tiết kiệm CPU)
        payload = jwt.decode(token, options={"verify_signature": False})
        exp = payload.get("exp")
        
        if exp:
            remaining = int(exp - datetime.utcnow().timestamp())
            return max(remaining, 0)  # Không âm
        
        return 0
        
    except Exception:
        return 0
```
**Giải thích:**
- `options={"verify_signature": False}`: Chỉ decode để lấy exp, không verify signature
- Tính thời gian còn lại để set TTL cho blacklist
- `max(remaining, 0)`: Đảm bảo không trả về số âm

---

## 🔐 Bước 3: Cập nhật Middleware

### 3.1 Cập nhật `app/middleware/auth_middleware.py`

```python
# Import function mới
from ..core.jwt_utils import verify_access_token_with_blacklist

class AuthenticationMiddleware(BaseHTTPMiddleware):
    # ... (code khác giữ nguyên)
    
    async def dispatch(self, request: Request, call_next: Callable):
        # ... (code trước giữ nguyên đến phần verify token)
        
        # Verify JWT token với blacklist check
        payload = verify_access_token_with_blacklist(token)
        if payload is None:
            print(f"❌ Invalid, expired, or blacklisted token: {token[:20]}...")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "detail": "Invalid, expired, or revoked token",
                    "error_code": "INVALID_TOKEN"
                }
            )
```
**Giải thích:**
- Thay `verify_token()` bằng `verify_access_token_with_blacklist()`
- Middleware tự động reject tokens bị blacklist
- Error message bao gồm "revoked token"

---

## 🚪 Bước 4: Implement Logout Functionality

### 4.1 Cập nhật `app/core/refresh_service.py`

```python
from .blacklist_service import blacklist_service
from .jwt_utils import calculate_token_remaining_time

class RefreshTokenService:
    # ... (code trước giữ nguyên)
    
    def logout_user(self, access_token: str, refresh_token: Optional[str] = None) -> bool:
        """
        Logout user và blacklist tokens
        
        Args:
            access_token: Access token cần blacklist
            refresh_token: Refresh token cần revoke (optional)
        
        Returns:
            bool: True nếu logout thành công
        """
        print(f"🚪 Logging out user with token: {access_token[:20]}...")
        
        success = True
        
        # Blacklist access token
        remaining_time = calculate_token_remaining_time(access_token)
        if remaining_time > 0:
            blacklist_success = blacklist_service.add_token_to_blacklist(
                access_token, 
                remaining_time, 
                "logout"
            )
            if not blacklist_success:
                success = False
        else:
            print("⚠️ Access token already expired, no need to blacklist")
```
**Giải thích:**
- Tính thời gian còn lại của access token
- Chỉ blacklist token còn hiệu lực
- TTL của blacklist = thời gian còn lại của token

```python
        # Revoke refresh token
        if refresh_token:
            revoke_success = self.revoke_refresh_token(refresh_token)
            if not revoke_success:
                success = False
        
        if success:
            print("✅ User logged out successfully")
        else:
            print("⚠️ Logout completed with some errors")
        
        return success
    
    def logout_all_sessions(self, user_id: int) -> bool:
        """
        Logout tất cả sessions của user (revoke tất cả refresh tokens)
        
        Args:
            user_id: ID của user
        
        Returns:
            bool: True nếu thành công
        """
        print(f"🚪 Logging out all sessions for user {user_id}")
        
        # Tìm và revoke tất cả refresh tokens của user
        tokens_to_remove = []
        for token in self.active_refresh_tokens:
            try:
                # Decode token để lấy user_id (không verify signature)
                payload = jwt.decode(token, options={"verify_signature": False})
                if int(payload.get("sub", 0)) == user_id:
                    tokens_to_remove.append(token)
            except Exception:
                continue
        
        # Remove tokens
        for token in tokens_to_remove:
            self.active_refresh_tokens.discard(token)
        
        print(f"✅ Revoked {len(tokens_to_remove)} refresh tokens for user {user_id}")
        return True
```
**Giải thích:**
- `logout_all_sessions()`: Revoke tất cả refresh tokens của user
- Hữu ích khi user đổi password hoặc phát hiện account bị compromise
- Decode token không verify signature để lấy user_id

---

## 🌐 Bước 5: Logout API Endpoints

### 5.1 Cập nhật `app/api/auth_basic.py`

```python
from fastapi import Request
from ..core.blacklist_service import blacklist_service

@router.post("/logout")
def logout(request: Request):
    """
    Logout user và blacklist current access token
    
    Args:
        request: FastAPI request (chứa user info từ middleware)
    
    Returns:
        dict: Logout confirmation
    """
    # Lấy user info từ middleware
    current_user = request.state.current_user
    
    # Lấy access token từ Authorization header
    authorization = request.headers.get("Authorization")
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Authorization header required for logout"
        )
    
    try:
        scheme, access_token = authorization.split(" ", 1)
        if scheme.lower() != "bearer":
            raise ValueError("Invalid scheme")
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid authorization header format"
        )
```
**Giải thích:**
- Lấy user info từ `request.state` (được set bởi middleware)
- Extract access token từ Authorization header
- Validate header format

```python
    # Logout user
    logout_success = refresh_service.logout_user(access_token)
    
    if logout_success:
        print(f"✅ User {current_user['username']} logged out successfully")
        return {
            "message": "Logged out successfully",
            "user": current_user["username"],
            "blacklisted_token": True
        }
    else:
        print(f"⚠️ Logout had some issues for user {current_user['username']}")
        return {
            "message": "Logged out with warnings",
            "user": current_user["username"],
            "blacklisted_token": False
        }

@router.post("/logout-with-refresh")
def logout_with_refresh(request: Request, logout_data: RefreshTokenRequest):
    """
    Logout với cả access token và refresh token
    
    Args:
        request: FastAPI request
        logout_data: Chứa refresh token
    
    Returns:
        dict: Logout confirmation
    """
    current_user = request.state.current_user
    
    # Lấy access token
    authorization = request.headers.get("Authorization")
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Authorization header required"
        )
    
    try:
        scheme, access_token = authorization.split(" ", 1)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid authorization header"
        )
    
    # Logout với cả access và refresh token
    logout_success = refresh_service.logout_user(
        access_token=access_token,
        refresh_token=logout_data.refresh_token
    )
    
    return {
        "message": "Logged out successfully",
        "user": current_user["username"],
        "revoked_tokens": ["access_token", "refresh_token"],
        "success": logout_success
    }
```

### 5.2 Admin endpoints

```python
@router.post("/admin/logout-user/{user_id}")
def admin_logout_user(
    user_id: int,
    request: Request
):
    """
    Admin endpoint: Logout tất cả sessions của user khác
    
    Args:
        user_id: ID của user cần logout
        request: FastAPI request
    
    Returns:
        dict: Logout confirmation
    """
    current_user = request.state.current_user
    
    # Kiểm tra quyền admin (có thể dùng role middleware thay thế)
    if current_user["role"] != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    
    # Logout all sessions của user
    logout_success = refresh_service.logout_all_sessions(user_id)
    
    return {
        "message": f"All sessions logged out for user {user_id}",
        "admin": current_user["username"],
        "target_user_id": user_id,
        "success": logout_success
    }

@router.get("/admin/blacklist-stats")
def get_blacklist_stats(request: Request):
    """
    Admin endpoint: Lấy thống kê blacklist
    """
    current_user = request.state.current_user
    
    if current_user["role"] != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    
    stats = blacklist_service.get_blacklist_stats()
    refresh_stats = {
        "active_refresh_tokens": refresh_service.get_active_tokens_count()
    }
    
    return {
        "blacklist_stats": stats,
        "refresh_token_stats": refresh_stats,
        "admin": current_user["username"]
    }
```

---

## 🧪 Bước 6: Testing Logout Flow

### 6.1 Test complete logout flow

**1. Login:**
```bash
curl -X POST "http://localhost:8001/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "user1", "password": "user123"}'
```

**2. Sử dụng token:**
```bash
curl -X GET "http://localhost:8001/auth/me-middleware" \
  -H "Authorization: Bearer ACCESS_TOKEN"
```
**Expected:** ✅ Success

**3. Logout:**
```bash
curl -X POST "http://localhost:8001/auth/logout" \
  -H "Authorization: Bearer ACCESS_TOKEN"
```

**4. Thử sử dụng token sau logout:**
```bash
curl -X GET "http://localhost:8001/auth/me-middleware" \
  -H "Authorization: Bearer ACCESS_TOKEN"
```
**Expected:** ❌ 401 Unauthorized (token bị blacklist)

### 6.2 Test refresh token revocation

**1. Login và lấy cả access + refresh token**
**2. Logout với refresh token:**
```bash
curl -X POST "http://localhost:8001/auth/logout-with-refresh" \
  -H "Authorization: Bearer ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"refresh_token": "REFRESH_TOKEN"}'
```

**3. Thử refresh token:**
```bash
curl -X POST "http://localhost:8001/auth/refresh" \
  -H "Content-Type: application/json" \
  -d '{"refresh_token": "REFRESH_TOKEN"}'
```
**Expected:** ❌ 401 Unauthorized (refresh token đã bị revoke)

### 6.3 Redis verification

**Check Redis directly:**
```bash
redis-cli
> KEYS blacklist:*
> GET blacklist:eyJhbGciOiJIUzI1NiIs...
> TTL blacklist:eyJhbGciOiJIUzI1NiIs...
```

---

## 📝 Tóm tắt

### Token Blacklist Architecture:

```
Request → Middleware → Check Blacklist → Verify JWT → Allow/Deny
                           ↓
                      Redis/Memory
                    (Auto-expire TTL)
```

### Security Benefits:

- ✅ **Immediate Token Revocation**: Tokens bị blacklist ngay lập tức
- ✅ **Logout Security**: Token không thể sử dụng sau logout
- ✅ **Compromise Response**: Có thể revoke tokens khi phát hiện breach
- ✅ **Admin Control**: Admin có thể logout users khác
- ✅ **Auto Cleanup**: Tokens tự động xóa khỏi blacklist khi hết hạn

### Files đã tạo/cập nhật:

- `app/core/blacklist_service.py`: Redis blacklist service
- Updated `app/core/jwt_utils.py`: Blacklist-aware token verification
- Updated `app/middleware/auth_middleware.py`: Middleware với blacklist check
- Updated `app/core/refresh_service.py`: Logout functionality
- Updated `app/api/auth_basic.py`: Logout endpoints

### Production Considerations:

- ✅ **Redis High Availability**: Cluster/Sentinel setup
- ✅ **Monitoring**: Track blacklist size và performance
- ✅ **Backup Strategy**: Redis persistence configuration
- ✅ **Rate Limiting**: Prevent blacklist abuse

### Next steps:

- ✅ JWT cơ bản hoàn thành
- ✅ Middleware Authorization hoàn thành
- ✅ Refresh Token flow hoàn thành
- ✅ Logout + Blacklist hoàn thành
- 🔄 Tiếp theo: Advanced Features (Fingerprint, Metrics, Logging)

---

*Token Blacklist giải quyết vấn đề lớn nhất của JWT: Không thể revoke tokens!*
