# 🎯 Thực hành JWT Cơ Bản - Tạo + Verify + Bảo vệ Route

## 📋 Mục tiêu
- ✅ Tạo JWT token khi user login
- ✅ Verify JWT token từ request header
- ✅ Bảo vệ route `/protected` với JWT
- ✅ Test với Postman/curl

---

## 🛠️ Bước 1: Setup JWT Utilities

### 1.1 Tạo file `app/core/jwt_utils.py`

```python
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from fastapi import HTTPException, status

# Cấu hình JWT
SECRET_KEY = "your-super-secret-key-change-in-production-32-chars-minimum"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_jwt_token(user_id: int, username: str, role: str = "user") -> str:
    """
    Tạo JWT access token
    
    Args:
        user_id: ID của user trong database
        username: <PERSON><PERSON><PERSON> đăng nhập
        role: <PERSON><PERSON> tr<PERSON> của user (user, admin, moderator)
    
    Returns:
        str: JWT token string
    """
    # Tính thời gian hết hạn (30 phút từ bây giờ)
    expire_time = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
```
**Giải thích:**
- `datetime.utcnow()`: Lấy thời gian hiện tại theo UTC (không phụ thuộc timezone)
- `timedelta(minutes=30)`: Thêm 30 phút → token sẽ hết hạn sau 30 phút
- Thời gian ngắn để tăng bảo mật, nếu token bị đánh cắp thì chỉ sử dụng được 30 phút

```python
    # Tạo payload (dữ liệu) cho JWT
    payload = {
        "sub": str(user_id),           # Subject - ID của user (phải là string)
        "username": username,          # Tên user để dễ debug
        "role": role,                  # Vai trò để phân quyền
        "iat": datetime.utcnow(),      # Issued At - thời gian tạo token
        "exp": expire_time             # Expiration - thời gian hết hạn
    }
```
**Giải thích từng field:**
- `sub` (Subject): JWT standard, chứa ID của user, phải là string
- `username`: Thông tin bổ sung, giúp debug và hiển thị
- `role`: Dùng để phân quyền (admin có thể truy cập nhiều hơn user)
- `iat` (Issued At): Thời gian tạo token, dùng để audit
- `exp` (Expiration): JWT tự động kiểm tra hết hạn

```python
    # Mã hóa payload thành JWT token
    try:
        token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
        print(f"✅ Token created for user {username} (ID: {user_id})")
        return token
    except Exception as e:
        print(f"❌ Error creating token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not create token"
        )
```
**Giải thích:**
- `jwt.encode()`: Hàm mã hóa payload thành JWT string
- Cần 3 tham số: payload, secret_key, algorithm
- Try-catch để handle lỗi khi tạo token
- Log để debug và monitor

### 1.2 Hàm verify JWT token

```python
def verify_jwt_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Kiểm tra và giải mã JWT token
    
    Args:
        token: JWT token string từ client
    
    Returns:
        Dict: Payload của token nếu hợp lệ, None nếu không hợp lệ
    """
    try:
        # Giải mã JWT token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        print(f"✅ Token decoded successfully for user: {payload.get('username')}")
```
**Giải thích:**
- `jwt.decode()`: Giải mã JWT token thành payload
- Cần cung cấp: token, secret_key, algorithms list
- Nếu token bị sửa đổi hoặc sai secret key → JWTError

```python
        # Kiểm tra token có hết hạn chưa
        exp_timestamp = payload.get("exp")
        if exp_timestamp:
            exp_datetime = datetime.fromtimestamp(exp_timestamp)
            current_time = datetime.utcnow()
            
            if exp_datetime < current_time:
                print(f"❌ Token expired at {exp_datetime}, current time: {current_time}")
                return None
```
**Giải thích:**
- `payload.get("exp")`: Lấy timestamp hết hạn từ payload
- `datetime.fromtimestamp()`: Chuyển timestamp thành datetime object
- So sánh với thời gian hiện tại để kiểm tra hết hạn
- Return None nếu token đã hết hạn

```python
        # Token hợp lệ, return payload
        return payload
        
    except JWTError as e:
        print(f"❌ JWT Error: {e}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None
```
**Giải thích:**
- `JWTError`: Exception khi token không hợp lệ (sai format, sai signature, etc.)
- `Exception`: Catch tất cả lỗi khác có thể xảy ra
- Luôn return None khi có lỗi để đảm bảo security

---

## 🔐 Bước 2: Tạo Authentication Dependency

### 2.1 Tạo file `app/core/auth_deps.py`

```python
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing import Dict, Any
from .jwt_utils import verify_jwt_token

# HTTP Bearer scheme để lấy token từ Authorization header
security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Dependency để lấy thông tin user hiện tại từ JWT token
    
    Args:
        credentials: Bearer token từ Authorization header
    
    Returns:
        Dict: Thông tin user từ token payload
    
    Raises:
        HTTPException: Nếu token không hợp lệ
    """
    # Lấy token từ Authorization header
    token = credentials.credentials
    print(f"🔍 Checking token: {token[:20]}...")  # Chỉ log 20 ký tự đầu
```
**Giải thích:**
- `HTTPBearer()`: FastAPI security scheme tự động lấy token từ header
- Header format: "Authorization: Bearer <token>"
- `credentials.credentials`: Chứa token string (bỏ phần "Bearer ")

```python
    # Verify token
    payload = verify_jwt_token(token)
    if payload is None:
        print("❌ Token verification failed")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
```
**Giải thích:**
- Gọi hàm `verify_jwt_token()` để kiểm tra token
- Nếu return None → token không hợp lệ hoặc hết hạn
- `HTTP_401_UNAUTHORIZED`: Status code 401 = Unauthorized (chưa xác thực)
- `WWW-Authenticate` header: Báo client cần Bearer token

```python
    # Lấy thông tin user từ payload
    user_id = payload.get("sub")
    username = payload.get("username")
    role = payload.get("role")
    
    if not user_id:
        print("❌ Token missing user ID")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
```
**Giải thích:**
- Lấy các field cần thiết từ token payload
- `sub`: User ID (Subject của JWT)
- Kiểm tra user_id có tồn tại không
- Nếu không có user_id → token format không đúng

```python
    print(f"✅ User authenticated: {username} (ID: {user_id}, Role: {role})")
    
    return {
        "user_id": int(user_id),    # Convert string về int
        "username": username,
        "role": role
    }
```
**Giải thích:**
- Log thông tin user đã authenticate thành công
- Return dictionary chứa thông tin user
- Convert user_id từ string về int (JWT lưu dưới dạng string)

### 2.2 Dependency cho Admin users

```python
def get_current_admin_user(current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """
    Dependency chỉ cho phép admin users
    
    Args:
        current_user: User info từ get_current_user
    
    Returns:
        Dict: Admin user info
    
    Raises:
        HTTPException: Nếu user không phải admin
    """
    if current_user["role"] != "admin":
        print(f"❌ Access denied for user {current_user['username']} (role: {current_user['role']})")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions. Admin role required."
        )
```
**Giải thích:**
- Dependency chain: `get_current_admin_user` → `get_current_user`
- Trước tiên verify token, sau đó kiểm tra role
- `HTTP_403_FORBIDDEN`: Status code 403 = Forbidden (không đủ quyền)
- Chỉ user có role "admin" mới pass được

```python
    print(f"✅ Admin access granted for {current_user['username']}")
    return current_user
```

---

## 🌐 Bước 3: Tạo API Endpoints

### 3.1 Tạo file `app/api/auth_basic.py`

```python
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel
from typing import Dict, Any
from ..core.jwt_utils import create_jwt_token
from ..core.auth_deps import get_current_user, get_current_admin_user

router = APIRouter(prefix="/auth", tags=["Authentication Basic"])

# Pydantic models cho request/response
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 1800  # 30 minutes in seconds
    user_info: Dict[str, Any]

# Fake user database cho demo
FAKE_USERS = {
    "admin": {"password": "admin123", "role": "admin", "user_id": 1},
    "user1": {"password": "user123", "role": "user", "user_id": 2},
    "user2": {"password": "user456", "role": "user", "user_id": 3}
}
```
**Giải thích:**
- `BaseModel`: Pydantic models để validate request/response
- `FAKE_USERS`: Database giả để demo, production sẽ dùng real database
- Mỗi user có password, role, và user_id

### 3.2 Login endpoint

```python
@router.post("/login", response_model=LoginResponse)
def login(login_data: LoginRequest) -> LoginResponse:
    """
    Đăng nhập và tạo JWT token
    
    Args:
        login_data: Username và password
    
    Returns:
        LoginResponse: Token và thông tin user
    """
    username = login_data.username
    password = login_data.password
    
    print(f"🔐 Login attempt for username: {username}")
    
    # Kiểm tra user có tồn tại không
    if username not in FAKE_USERS:
        print(f"❌ User {username} not found")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
```
**Giải thích:**
- `@router.post()`: Định nghĩa POST endpoint
- `response_model`: FastAPI tự động validate response
- Kiểm tra username có trong database không
- Không nên báo cụ thể "user not found" để tránh username enumeration attack

```python
    user_data = FAKE_USERS[username]
    
    # Kiểm tra password
    if password != user_data["password"]:
        print(f"❌ Wrong password for user {username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
```
**Giải thích:**
- Lấy thông tin user từ database
- So sánh password (production sẽ dùng hash comparison)
- Cùng error message để tránh password enumeration

```python
    # Tạo JWT token
    try:
        token = create_jwt_token(
            user_id=user_data["user_id"],
            username=username,
            role=user_data["role"]
        )
        
        print(f"✅ Login successful for {username}")
        
        return LoginResponse(
            access_token=token,
            user_info={
                "user_id": user_data["user_id"],
                "username": username,
                "role": user_data["role"]
            }
        )
        
    except Exception as e:
        print(f"❌ Error during login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )
```
**Giải thích:**
- Gọi hàm `create_jwt_token()` để tạo token
- Return token cùng với thông tin user
- Try-catch để handle lỗi khi tạo token

---

## 🛡️ Bước 4: Protected Routes

### 4.1 Protected endpoints

```python
@router.get("/me")
def get_current_user_info(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Lấy thông tin user hiện tại (cần JWT token)
    
    Args:
        current_user: User info từ JWT token
    
    Returns:
        Dict: Thông tin user
    """
    print(f"📋 Getting info for user: {current_user['username']}")
    
    return {
        "message": f"Hello {current_user['username']}!",
        "user_info": current_user,
        "protected_data": "This data requires authentication"
    }
```
**Giải thích:**
- `Depends(get_current_user)`: FastAPI tự động gọi dependency
- Nếu token hợp lệ → current_user chứa thông tin user
- Nếu token không hợp lệ → FastAPI tự động return 401 error

```python
@router.get("/protected")
def protected_route(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Route được bảo vệ - chỉ user có token hợp lệ mới truy cập được
    """
    return {
        "message": "🔒 This is a protected route!",
        "accessed_by": current_user["username"],
        "user_role": current_user["role"],
        "sensitive_data": [
            "User's private information",
            "Confidential business data",
            "Protected resources"
        ]
    }

@router.get("/admin-only")
def admin_only_route(admin_user: Dict[str, Any] = Depends(get_current_admin_user)):
    """
    Route chỉ dành cho admin
    """
    return {
        "message": "👑 Admin-only content!",
        "admin_user": admin_user["username"],
        "admin_data": {
            "total_users": len(FAKE_USERS),
            "system_status": "All systems operational",
            "admin_privileges": ["manage_users", "view_logs", "system_config"]
        }
    }
```
**Giải thích:**
- `/protected`: Cần token hợp lệ (bất kỳ role nào)
- `/admin-only`: Cần token hợp lệ VÀ role = "admin"
- FastAPI tự động handle authentication và authorization

---

## 🧪 Bước 5: Testing

### 5.1 Cập nhật `app/main.py`

```python
from fastapi import FastAPI
from .api.auth_basic import router as auth_router

app = FastAPI(title="JWT Basic Practice", version="1.0.0")

# Include auth router
app.include_router(auth_router)

@app.get("/")
def root():
    return {"message": "JWT Basic Practice API", "docs": "/docs"}
```

### 5.2 Test với curl

**1. Login để lấy token:**
```bash
curl -X POST "http://localhost:8001/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_info": {
    "user_id": 1,
    "username": "admin",
    "role": "admin"
  }
}
```

**2. Truy cập protected route:**
```bash
curl -X GET "http://localhost:8001/auth/protected" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

**3. Test admin route:**
```bash
curl -X GET "http://localhost:8001/auth/admin-only" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN_HERE"
```

### 5.3 Test cases

**✅ Successful cases:**
- Login với đúng username/password
- Truy cập protected route với valid token
- Admin truy cập admin-only route

**❌ Error cases:**
- Login với sai password → 401
- Truy cập protected route không có token → 401
- Truy cập protected route với invalid token → 401
- User thường truy cập admin route → 403
- Truy cập với expired token → 401

---

## 📝 Tóm tắt

### Quy trình JWT cơ bản:

1. **Login** → Verify credentials → Tạo JWT token
2. **Client lưu token** → localStorage hoặc memory
3. **Gửi request** → Header "Authorization: Bearer <token>"
4. **Server verify** → Dependency check token → Allow/Deny
5. **Response** → Trả data hoặc error

### Files đã tạo:

- `app/core/jwt_utils.py`: Tạo và verify JWT tokens
- `app/core/auth_deps.py`: FastAPI dependencies cho authentication
- `app/api/auth_basic.py`: Login và protected endpoints

### Next steps:

- ✅ JWT cơ bản hoàn thành
- 🔄 Tiếp theo: Middleware Authorization
- 🔄 Sau đó: Refresh Token flow

---

*Chạy server và test với Swagger UI tại `http://localhost:8001/docs` để thấy kết quả trực quan!*
