# Hướng Dẫn Thư <PERSON>c API - <PERSON><PERSON><PERSON> Cho Người Mới

## 📋 Mục Lụ<PERSON>
1. [Th<PERSON> mục API là gì?](#thư-mục-api-là-gì)
2. [Cấu trúc thư mục API](#cấu-trúc-thư-mục-api)
3. [Versioning API (v1, v2...)](#versioning-api)
4. [So sánh với thư mục Routes](#so-sánh-với-thư-mục-routes)
5. [Cách sử dụng thực tế](#cách-sử-dụng-thực-tế)
6. [Best Practices](#best-practices)

---

## 🎯 Thư mục API là gì?

Thư mục `api` trong dự án backend Python được sử dụng để **tổ chức và quản lý các endpoint API theo phiên bản (version)**. Đ<PERSON><PERSON> là một pattern phổ biến trong việc xây dựng RESTful API.

### <PERSON><PERSON><PERSON> đích chính:
- **Versioning**: Quản lý các phiên bản API khác nhau (v1, v2, v3...)
- **Tổ chức**: Nhóm các endpoint theo logic nghiệp vụ
- **Bảo trì**: Dễ dàng maintain và upgrade API
- **Backward Compatibility**: Hỗ trợ nhiều phiên bản API cùng lúc

---

## 🏗️ Cấu trúc thư mục API

Trong dự án của bạn:

```
Auth_services/
├── app/
│   ├── api/                    # Thư mục chính cho API
│   │   └── v1/                 # Phiên bản 1 của API
│   │       ├── __init__.py     # File khởi tạo module
│   │       └── deps.py         # Dependencies cho API v1
│   ├── routes/                 # Thư mục routes (cách tổ chức khác)
│   │   └── auth_routes.py      # Routes cho authentication
│   ├── controllers/            # Business logic
│   ├── services/              # Service layer
│   └── models/                # Database models
```

### Giải thích từng thành phần:

#### 📁 `api/`
- **Thư mục gốc** chứa tất cả các phiên bản API
- Tách biệt logic API khỏi các thành phần khác

#### 📁 `api/v1/`
- **Phiên bản 1** của API
- Chứa tất cả endpoints cho version 1
- Có thể có `v2/`, `v3/` trong tương lai

#### 📄 `deps.py`
- **Dependencies** dùng chung cho API v1
- Chứa các function dependency injection
- Ví dụ: authentication, database session, validation

---

## 🔄 Versioning API

### Tại sao cần Versioning?

```python
# Ví dụ: API v1 trả về dữ liệu đơn giản
# GET /api/v1/users/1
{
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>"
}

# API v2 trả về dữ liệu phong phú hơn
# GET /api/v2/users/1
{
    "id": 1,
    "profile": {
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe"
    },
    "contact": {
        "email": "<EMAIL>",
        "phone": "+1234567890"
    },
    "metadata": {
        "created_at": "2024-01-01T00:00:00Z",
        "last_login": "2024-01-15T10:30:00Z"
    }
}
```

### Cách tổ chức Versioning:

```python
# api/v1/__init__.py
from fastapi import APIRouter
from .endpoints import auth, users

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])

# api/v2/__init__.py  
from fastapi import APIRouter
from .endpoints import auth, users, profiles

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth-v2"])
api_router.include_router(users.router, prefix="/users", tags=["users-v2"])
api_router.include_router(profiles.router, prefix="/profiles", tags=["profiles"])
```

---

## ⚖️ So sánh với thư mục Routes

| Khía cạnh | `api/` | `routes/` |
|-----------|--------|-----------|
| **Mục đích** | Tổ chức theo version API | Tổ chức theo chức năng |
| **Cấu trúc** | `api/v1/`, `api/v2/` | `routes/auth.py`, `routes/users.py` |
| **Sử dụng** | Dự án lớn, nhiều version | Dự án nhỏ, đơn giản |
| **Ưu điểm** | Dễ maintain, scalable | Đơn giản, trực quan |
| **Nhược điểm** | Phức tạp hơn | Khó scale khi dự án lớn |

### Trong dự án của bạn:
Hiện tại bạn đang sử dụng **cả hai cách**:
- `routes/auth_routes.py`: Chứa logic routing
- `api/v1/`: Chuẩn bị cho versioning (hiện tại chưa sử dụng)

---

## 💡 Cách sử dụng thực tế

### Bước 1: Tạo cấu trúc API v1

```python
# api/v1/endpoints/auth.py
from fastapi import APIRouter, Depends
from ...controllers.auth_controllers import AuthController
from ...schemas.auth import RegisterRequest, UserResponse

router = APIRouter()

@router.post("/register", response_model=UserResponse)
def register(
    payload: RegisterRequest, 
    controller: AuthController = Depends()
):
    return controller.register(payload)

@router.post("/login")
def login(payload: LoginRequest):
    # Logic đăng nhập
    pass
```

### Bước 2: Tạo dependencies

```python
# api/v1/deps.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from ...core.database import get_db
from ...services.auth_services import AuthService

security = HTTPBearer()

def get_current_user(
    token: str = Depends(security),
    db: Session = Depends(get_db)
):
    """
    Dependency để lấy user hiện tại từ token
    """
    auth_service = AuthService(db)
    user = auth_service.get_user_from_token(token.credentials)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    return user

def get_admin_user(
    current_user = Depends(get_current_user)
):
    """
    Dependency để kiểm tra user có quyền admin
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user
```

### Bước 3: Tích hợp vào main app

```python
# api/v1/__init__.py
from fastapi import APIRouter
from .endpoints import auth

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])

# main.py
from fastapi import FastAPI
from .api.v1 import api_router as api_v1_router
from .routes.auth_routes import router as auth_router

app = FastAPI(title="Auth Service API")

# Cách cũ (routes)
app.include_router(auth_router)

# Cách mới (api versioning)
app.include_router(api_v1_router, prefix="/api/v1")
```

---

## 🎯 Best Practices

### 1. **Naming Convention**
```python
# ✅ Tốt
api/v1/endpoints/auth.py
api/v1/endpoints/users.py
api/v1/endpoints/products.py

# ❌ Không tốt
api/v1/auth_endpoint.py
api/v1/user_api.py
```

### 2. **Dependencies Organization**
```python
# api/v1/deps.py
def get_db():
    """Database session dependency"""
    pass

def get_current_user():
    """Current user dependency"""
    pass

def require_admin():
    """Admin permission dependency"""
    pass
```

### 3. **Error Handling**
```python
# api/v1/endpoints/auth.py
from fastapi import HTTPException, status

@router.post("/login")
def login(payload: LoginRequest):
    try:
        result = auth_service.login(payload)
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
```

### 4. **Documentation**
```python
@router.post(
    "/register",
    response_model=UserResponse,
    summary="Đăng ký người dùng mới",
    description="Tạo tài khoản người dùng mới với username và password",
    responses={
        201: {"description": "Đăng ký thành công"},
        400: {"description": "Dữ liệu không hợp lệ"},
        409: {"description": "Username đã tồn tại"}
    }
)
def register(payload: RegisterRequest):
    pass
```

---

## 📝 Ví dụ thực tế cho dự án Wound Detection

### Cấu trúc API cho dự án của bạn:

```
Auth_services/app/api/
├── v1/
│   ├── __init__.py
│   ├── deps.py                 # Dependencies chung
│   └── endpoints/
│       ├── __init__.py
│       ├── auth.py            # Authentication endpoints
│       ├── users.py           # User management
│       └── wounds.py          # Wound detection endpoints
└── v2/                        # Phiên bản tương lai
    ├── __init__.py
    └── endpoints/
        └── wounds.py          # Enhanced wound detection
```

### Code mẫu cho Wound Detection API:

```python
# api/v1/endpoints/wounds.py
from fastapi import APIRouter, Depends, UploadFile, File
from typing import List
from ...schemas.wound import WoundAnalysisResponse, WoundCreateRequest
from ...services.wound_service import WoundService
from .deps import get_current_user

router = APIRouter()

@router.post("/analyze", response_model=WoundAnalysisResponse)
async def analyze_wound(
    image: UploadFile = File(...),
    current_user = Depends(get_current_user),
    wound_service: WoundService = Depends()
):
    """
    Phân tích hình ảnh vết thương bằng AI

    - **image**: File hình ảnh vết thương (JPG, PNG)
    - **returns**: Kết quả phân tích bao gồm loại vết thương, mức độ nghiêm trọng
    """
    # Validate file type
    if image.content_type not in ["image/jpeg", "image/png"]:
        raise HTTPException(400, "Chỉ hỗ trợ file JPG và PNG")

    # Analyze wound using AI
    analysis_result = await wound_service.analyze_wound_image(
        image_file=image,
        user_id=current_user.id
    )

    return analysis_result

@router.get("/history", response_model=List[WoundAnalysisResponse])
def get_wound_history(
    current_user = Depends(get_current_user),
    wound_service: WoundService = Depends()
):
    """
    Lấy lịch sử phân tích vết thương của user
    """
    return wound_service.get_user_wound_history(current_user.id)
```

---

## 🛠️ Hướng dẫn Implementation

### Bước 1: Tạo cấu trúc thư mục

```bash
# Tạo thư mục endpoints
mkdir -p Auth_services/app/api/v1/endpoints

# Tạo các file cần thiết
touch Auth_services/app/api/v1/endpoints/__init__.py
touch Auth_services/app/api/v1/endpoints/auth.py
touch Auth_services/app/api/v1/endpoints/wounds.py
```

### Bước 2: Cập nhật deps.py

```python
# api/v1/deps.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from ...core.database import get_db
from ...services.auth_services import AuthService
from ...services.wound_service import WoundService

security = HTTPBearer()

def get_current_user(
    token: str = Depends(security),
    db: Session = Depends(get_db)
):
    """Lấy thông tin user từ JWT token"""
    auth_service = AuthService(db)
    user = auth_service.verify_token(token.credentials)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token không hợp lệ"
        )
    return user

def get_wound_service(db: Session = Depends(get_db)) -> WoundService:
    """Dependency injection cho WoundService"""
    return WoundService(db)
```

### Bước 3: Tích hợp vào main app

```python
# api/v1/__init__.py
from fastapi import APIRouter
from .endpoints import auth, wounds

api_router = APIRouter()

# Include các router endpoints
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"]
)
api_router.include_router(
    wounds.router,
    prefix="/wounds",
    tags=["Wound Analysis"]
)

# main.py - cập nhật
from .api.v1 import api_router as api_v1_router

# Thêm API v1 router
app.include_router(
    api_v1_router,
    prefix="/api/v1",
    responses={404: {"description": "Not found"}}
)
```

---

## 🧪 Testing API Structure

### Test file structure:
```
tests/
├── api/
│   └── v1/
│       ├── test_auth.py
│       └── test_wounds.py
└── conftest.py
```

### Ví dụ test:
```python
# tests/api/v1/test_wounds.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_analyze_wound_unauthorized():
    """Test phân tích vết thương khi chưa đăng nhập"""
    response = client.post("/api/v1/wounds/analyze")
    assert response.status_code == 401

def test_analyze_wound_success(auth_headers, sample_wound_image):
    """Test phân tích vết thương thành công"""
    response = client.post(
        "/api/v1/wounds/analyze",
        files={"image": sample_wound_image},
        headers=auth_headers
    )
    assert response.status_code == 200
    assert "wound_type" in response.json()
```

---

## 🚀 Kết luận

Thư mục `api` là một pattern quan trọng trong việc xây dựng API có thể mở rộng và bảo trì. Nó giúp:

1. **Tổ chức code** theo phiên bản
2. **Hỗ trợ backward compatibility**
3. **Dễ dàng testing** và deployment
4. **Chuẩn hóa** cấu trúc dự án

### Lợi ích cho dự án Wound Detection:
- **Scalability**: Dễ dàng thêm tính năng mới
- **Maintainability**: Code được tổ chức rõ ràng
- **Professional**: Cấu trúc chuẩn enterprise
- **Documentation**: API docs tự động với FastAPI

### Bước tiếp theo:
1. Implement cấu trúc API v1 theo hướng dẫn
2. Migrate existing routes sang API structure
3. Thêm comprehensive testing
4. Setup API documentation với Swagger UI

Chúc bạn thành công với dự án capstone! 🎯
