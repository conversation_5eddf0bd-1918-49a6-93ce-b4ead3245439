# 💻 Ví dụ thực hành Authentication & Authorization

## 📋 <PERSON>ụ<PERSON> lục
1. [Setup cơ bản](#setup-c<PERSON>-bản)
2. [Tạo JWT Token](#tạo-jwt-token)
3. [Authentication Middleware](#authentication-middleware)
4. [Protected Routes](#protected-routes)
5. [Role-based Authorization](#role-based-authorization)

---

## 🛠️ Setup cơ bản

### 1. Cài đặt dependencies

```bash
pip install python-jose[cryptography] passlib[bcrypt] python-multipart
```

### 2. C<PERSON>u trú<PERSON> thư mục
```
app/
├── core/
│   ├── __init__.py
│   ├── config.py          # Cấu hình
│   ├── security.py        # JWT utilities
│   └── deps.py           # Dependencies
├── models/
│   └── user.py           # User model
├── schemas/
│   └── auth.py           # Pydantic schemas
└── api/
    └── auth.py           # Auth endpoints
```

---

## 🔧 Tạo JWT Token

### 1. File c<PERSON><PERSON> hình (app/core/config.py)

```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # JWT Settings
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Database
    DATABASE_URL: str = "sqlite:///./auth.db"
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### 2. Security utilities (app/core/security.py)

```python
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from .config import settings

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify plain password với hashed password
    
    Args:
        plain_password: Password người dùng nhập
        hashed_password: Password đã hash trong database
    
    Returns:
        bool: True nếu password đúng
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    Hash password sử dụng bcrypt
    
    Args:
        password: Plain text password
    
    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)

def create_access_token(
    subject: Union[str, int], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Tạo JWT access token
    
    Args:
        subject: User ID hoặc username
        expires_delta: Thời gian hết hạn (optional)
    
    Returns:
        str: JWT token
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    # Payload của JWT
    to_encode = {
        "exp": expire,           # Expiration time
        "iat": datetime.utcnow(), # Issued at
        "sub": str(subject)      # Subject (user ID)
    }
    
    # Tạo JWT token
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt

def create_refresh_token(subject: Union[str, int]) -> str:
    """
    Tạo refresh token với thời gian sống lâu hơn
    
    Args:
        subject: User ID
    
    Returns:
        str: Refresh token
    """
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode = {
        "exp": expire,
        "iat": datetime.utcnow(),
        "sub": str(subject),
        "type": "refresh"  # Đánh dấu đây là refresh token
    }
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """
    Verify và decode JWT token
    
    Args:
        token: JWT token string
    
    Returns:
        dict: Payload của token nếu valid, None nếu invalid
    """
    try:
        # Decode JWT token
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        
        # Kiểm tra expiration
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            return None
            
        return payload
        
    except JWTError as e:
        print(f"JWT Error: {e}")
        return None
    except Exception as e:
        print(f"Token verification error: {e}")
        return None
```

### 3. User model (app/models/user.py)

```python
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class User(Base):
    """
    User model cho authentication
    """
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    
    # Role và permissions
    role = Column(String(20), default="user")  # user, admin, moderator
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"
    
    def has_permission(self, permission: str) -> bool:
        """
        Kiểm tra user có permission cụ thể không
        
        Args:
            permission: Tên permission cần kiểm tra
        
        Returns:
            bool: True nếu có permission
        """
        # Role-based permissions
        role_permissions = {
            "admin": ["read", "write", "delete", "manage_users"],
            "moderator": ["read", "write", "moderate"],
            "user": ["read", "write_own"]
        }
        
        user_permissions = role_permissions.get(self.role, [])
        return permission in user_permissions
    
    def is_admin(self) -> bool:
        """Kiểm tra user có phải admin không"""
        return self.role == "admin"
    
    def is_moderator(self) -> bool:
        """Kiểm tra user có phải moderator không"""
        return self.role in ["admin", "moderator"]
```

### 4. Pydantic schemas (app/schemas/auth.py)

```python
from typing import Optional
from pydantic import BaseModel, EmailStr

class UserBase(BaseModel):
    """Base schema cho User"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    """Schema cho tạo user mới"""
    password: str

class UserLogin(BaseModel):
    """Schema cho login"""
    username: str
    password: str

class UserResponse(UserBase):
    """Schema cho response user info"""
    id: int
    role: str
    is_active: bool
    is_verified: bool
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    """Schema cho JWT token response"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # Seconds

class TokenData(BaseModel):
    """Schema cho token payload"""
    user_id: Optional[int] = None
    username: Optional[str] = None
```

---

## 🔐 Authentication Middleware

### 1. Dependencies (app/core/deps.py)

```python
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from .security import verify_token
from ..models.user import User
from ..database import get_db

# HTTP Bearer token scheme
security = HTTPBearer()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    Dependency để lấy current user từ JWT token
    
    Args:
        credentials: Bearer token từ header
        db: Database session
    
    Returns:
        User: Current user object
    
    Raises:
        HTTPException: Nếu token invalid hoặc user không tồn tại
    """
    # Lấy token từ Authorization header
    token = credentials.credentials
    
    # Verify token
    payload = verify_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Lấy user ID từ token payload
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Lấy user từ database
    user = db.query(User).filter(User.id == int(user_id)).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    # Kiểm tra user có active không
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return user

def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Dependency để lấy active user
    
    Args:
        current_user: User từ get_current_user
    
    Returns:
        User: Active user
    
    Raises:
        HTTPException: Nếu user không active
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Dependency để lấy admin user
    
    Args:
        current_user: User từ get_current_active_user
    
    Returns:
        User: Admin user
    
    Raises:
        HTTPException: Nếu user không phải admin
    """
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

def require_permission(permission: str):
    """
    Dependency factory để kiểm tra permission cụ thể
    
    Args:
        permission: Tên permission cần kiểm tra
    
    Returns:
        function: Dependency function
    """
    def permission_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not current_user.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    
    return permission_dependency
```

---

## 🛡️ Protected Routes

### 1. Auth endpoints (app/api/auth.py)

```python
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from ..core.config import settings
from ..core.security import (
    verify_password, 
    get_password_hash, 
    create_access_token,
    create_refresh_token
)
from ..core.deps import get_current_user, get_current_admin_user
from ..models.user import User
from ..schemas.auth import UserCreate, UserResponse, Token, UserLogin
from ..database import get_db

router = APIRouter(prefix="/auth", tags=["Authentication"])

@router.post("/register", response_model=UserResponse)
def register(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Đăng ký user mới
    
    Args:
        user_data: Thông tin user cần tạo
        db: Database session
    
    Returns:
        UserResponse: Thông tin user đã tạo
    """
    # Kiểm tra username đã tồn tại chưa
    existing_user = db.query(User).filter(
        (User.username == user_data.username) | 
        (User.email == user_data.email)
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already registered"
        )
    
    # Hash password
    hashed_password = get_password_hash(user_data.password)
    
    # Tạo user mới
    db_user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        hashed_password=hashed_password
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

@router.post("/login", response_model=Token)
def login(
    user_credentials: UserLogin,
    db: Session = Depends(get_db)
):
    """
    Đăng nhập và tạo JWT token
    
    Args:
        user_credentials: Username và password
        db: Database session
    
    Returns:
        Token: Access token và refresh token
    """
    # Tìm user trong database
    user = db.query(User).filter(
        User.username == user_credentials.username
    ).first()
    
    # Kiểm tra user tồn tại và password đúng
    if not user or not verify_password(user_credentials.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Kiểm tra user có active không
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    # Tạo access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.id,
        expires_delta=access_token_expires
    )
    
    # Tạo refresh token
    refresh_token = create_refresh_token(subject=user.id)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@router.get("/me", response_model=UserResponse)
def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Lấy thông tin user hiện tại
    
    Args:
        current_user: User từ JWT token
    
    Returns:
        UserResponse: Thông tin user
    """
    return current_user

@router.get("/admin-only")
def admin_only_endpoint(
    current_user: User = Depends(get_current_admin_user)
):
    """
    Endpoint chỉ admin mới truy cập được
    
    Args:
        current_user: Admin user
    
    Returns:
        dict: Message cho admin
    """
    return {
        "message": f"Hello admin {current_user.username}!",
        "admin_data": "This is sensitive admin data"
    }
```

---

*Tài liệu này tiếp tục với các ví dụ về Role-based Authorization và Advanced Security trong phần tiếp theo...*
