from pydantic import BaseModel

class UserBase(BaseModel): 
    username:str 

class RegisterRequest(UserBase): 
    password: str 

class LoginRequest(UserBase): 
    password: str 
    
class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"

class CurrentUser(BaseModel):
    user_id: int
    username: str
    
class UserResponse(UserBase):
    id: int

    class Config:
        from_attributes = True