from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker, Session
from .config import settings
from typing import Generator
 
engine = create_engine(
    settings.get_db_url, 
    pool_pre_ping=True,
    pool_recycle=300,
)

SessionLocal = sessionmaker(
    autocommit = False, 
    autoflush= False, 
    bind = engine
)

Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try: 
        yield db 
    finally: 
        db.close()