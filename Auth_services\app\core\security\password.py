from argon2 import PasswordHasher 

class HashedPassword: 
    
    def __init__(self): 
        self.ph = PasswordHasher()
    
    def hash_password(self, password: str) -> str:
        return self.ph.hash(password= password)
     
    def verify_password(self, password: str, hashed: str) -> bool:
        try:
            return self.ph.verify(hashed, password)
        except Exception:
            return False
