# 🚀 JWT Nâng Cao - Advanced Features

## 📋 Mục lục
1. [Refresh Token System](#refresh-token-system)
2. [Token Blacklist](#token-blacklist)
3. [Multiple Token Types](#multiple-token-types)
4. [Advanced Security](#advanced-security)
5. [Production Best Practices](#production-best-practices)

---

## 🔄 Refresh Token System

### Tại sao cần Refresh Token?

**Vấn đề với Access Token:**
- Access token có thời gian sống ngắn (15-30 phút)
- User phải đăng nhập lại liên tục → UX kém
- Nếu làm access token sống lâu → rủi ro bảo mật cao

**Giải pháp Refresh Token:**
- Access token: Ngắn hạn (30 phút)
- Refresh token: Dài hạn (7-30 ngày)
- Khi access token hết hạn → dùng refresh token để lấy access token mới

### 1. Tạo Refresh Token

```python
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
import secrets

# Cấu hình nâng cao
SECRET_KEY = "your-super-secret-key-minimum-32-characters-long"
REFRESH_SECRET_KEY = "different-secret-key-for-refresh-tokens"  # Key riêng cho refresh token
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

def create_access_token(user_id: int, username: str, role: str = "user") -> str:
    """
    Tạo access token với thêm thông tin role
    """
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    payload = {
        "sub": str(user_id),        # User ID
        "username": username,       # Username
        "role": role,              # User role (user, admin, moderator)
        "type": "access",          # Loại token
        "iat": datetime.utcnow(),  # Issued at
        "exp": expire              # Expiration
    }
```
**Giải thích payload nâng cao:**
- `role`: Thêm thông tin phân quyền vào token
- `type`: Phân biệt access token và refresh token
- Giúp server biết token này dùng để làm gì

```python
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    return token

def create_refresh_token(user_id: int) -> str:
    """
    Tạo refresh token với thời gian sống lâu hơn
    """
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    
    # Tạo unique identifier cho refresh token
    jti = secrets.token_urlsafe(32)  # JWT ID - unique cho mỗi token
```
**Giải thích:**
- `timedelta(days=7)`: Refresh token sống 7 ngày
- `secrets.token_urlsafe(32)`: Tạo ID ngẫu nhiên 32 bytes
- `jti` (JWT ID): Giúp track và revoke token cụ thể

```python
    payload = {
        "sub": str(user_id),
        "type": "refresh",         # Đánh dấu đây là refresh token
        "jti": jti,               # JWT ID để tracking
        "iat": datetime.utcnow(),
        "exp": expire
    }
    
    # Sử dụng secret key riêng cho refresh token
    token = jwt.encode(payload, REFRESH_SECRET_KEY, algorithm=ALGORITHM)
    return token
```
**Giải thích:**
- `type: "refresh"`: Phân biệt với access token
- `REFRESH_SECRET_KEY`: Key riêng biệt tăng bảo mật
- Nếu access token bị compromise, refresh token vẫn an toàn

### 2. Verify Refresh Token

```python
def verify_refresh_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify refresh token và kiểm tra loại token
    """
    try:
        # Decode với refresh secret key
        payload = jwt.decode(token, REFRESH_SECRET_KEY, algorithms=[ALGORITHM])
```
**Giải thích:**
- Phải dùng `REFRESH_SECRET_KEY` để decode refresh token
- Nếu dùng sai key → JWTError sẽ được raise

```python
        # Kiểm tra loại token
        token_type = payload.get("type")
        if token_type != "refresh":
            print("Token không phải refresh token")
            return None
```
**Giải thích:**
- Đảm bảo token này thực sự là refresh token
- Ngăn chặn việc dùng access token để refresh

```python
        # Kiểm tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            print("Refresh token đã hết hạn")
            return None
            
        return payload
        
    except JWTError as e:
        print(f"Lỗi verify refresh token: {e}")
        return None
```

### 3. Refresh Token Flow

```python
def refresh_access_token(refresh_token: str) -> Optional[Dict[str, str]]:
    """
    Tạo access token mới từ refresh token
    
    Returns:
        Dict chứa access_token và refresh_token mới (optional)
    """
    # Verify refresh token
    payload = verify_refresh_token(refresh_token)
    if not payload:
        return None
```
**Giải thích:**
- Kiểm tra refresh token có hợp lệ không
- Nếu không hợp lệ → user phải đăng nhập lại

```python
    # Lấy thông tin user từ refresh token
    user_id = payload.get("sub")
    if not user_id:
        return None
    
    # TODO: Lấy thông tin user từ database
    # user = get_user_by_id(int(user_id))
    # if not user or not user.is_active:
    #     return None
    
    # Giả sử lấy được user info
    username = "user_from_db"  # Thay bằng data từ DB
    role = "user"              # Thay bằng data từ DB
```
**Giải thích:**
- Lấy user_id từ refresh token payload
- Cần query database để lấy thông tin user mới nhất
- Kiểm tra user vẫn còn active (chưa bị ban/delete)

```python
    # Tạo access token mới
    new_access_token = create_access_token(
        user_id=int(user_id),
        username=username,
        role=role
    )
    
    # Optional: Tạo refresh token mới (rotate refresh token)
    new_refresh_token = create_refresh_token(int(user_id))
    
    return {
        "access_token": new_access_token,
        "refresh_token": new_refresh_token,  # Token mới
        "token_type": "bearer"
    }
```
**Giải thích:**
- Tạo access token mới với thông tin user hiện tại
- **Refresh Token Rotation**: Tạo refresh token mới thay thế token cũ
- Tăng bảo mật: Token cũ không thể sử dụng lại

---

## 🚫 Token Blacklist

### Tại sao cần Token Blacklist?

**Vấn đề:**
- JWT là stateless → Server không thể "thu hồi" token
- Khi user logout → Token vẫn có thể sử dụng đến khi hết hạn
- Nếu token bị đánh cắp → Không thể vô hiệu hóa

**Giải pháp:**
- Lưu danh sách token bị blacklist
- Kiểm tra mỗi khi verify token

### 1. Redis Blacklist Implementation

```python
import redis
from typing import Set

# Kết nối Redis
redis_client = redis.Redis(
    host='localhost',
    port=6379,
    db=0,
    decode_responses=True
)
```
**Giải thích:**
- Redis: In-memory database, rất nhanh cho blacklist
- `decode_responses=True`: Tự động decode bytes thành string

```python
class TokenBlacklist:
    """
    Quản lý blacklist tokens sử dụng Redis
    """
    
    @staticmethod
    def add_token(token: str, expires_in: int):
        """
        Thêm token vào blacklist
        
        Args:
            token: JWT token string
            expires_in: Thời gian hết hạn (seconds)
        """
        # Lưu token với TTL (Time To Live)
        redis_client.setex(f"blacklist:{token}", expires_in, "revoked")
```
**Giải thích:**
- `setex()`: Set key với expiration time
- `f"blacklist:{token}"`: Prefix để phân biệt với data khác
- `expires_in`: Token sẽ tự động xóa khỏi Redis khi hết hạn
- Value "revoked": Đánh dấu token bị thu hồi

```python
    @staticmethod
    def is_blacklisted(token: str) -> bool:
        """
        Kiểm tra token có trong blacklist không
        """
        return redis_client.exists(f"blacklist:{token}") > 0
```
**Giải thích:**
- `exists()`: Kiểm tra key có tồn tại trong Redis không
- Return True nếu token bị blacklist, False nếu không

```python
    @staticmethod
    def remove_token(token: str):
        """
        Xóa token khỏi blacklist (nếu cần)
        """
        redis_client.delete(f"blacklist:{token}")
```

### 2. Enhanced Token Verification

```python
def verify_token_with_blacklist(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify token với kiểm tra blacklist
    """
    try:
        # Kiểm tra blacklist trước
        if TokenBlacklist.is_blacklisted(token):
            print("Token đã bị blacklist")
            return None
```
**Giải thích:**
- Kiểm tra blacklist TRƯỚC khi decode token
- Tiết kiệm CPU: Không cần decode token đã bị blacklist
- Return None ngay lập tức nếu token bị blacklist

```python
        # Decode token như bình thường
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            print("Token đã hết hạn")
            return None
            
        return payload
        
    except JWTError as e:
        print(f"Lỗi JWT: {e}")
        return None
```

### 3. Logout Implementation

```python
def logout_user(token: str) -> bool:
    """
    Logout user và blacklist token
    
    Args:
        token: Access token cần blacklist
    
    Returns:
        bool: True nếu logout thành công
    """
    try:
        # Decode token để lấy expiration time
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        exp = payload.get("exp")
        
        if exp:
            # Tính thời gian còn lại của token
            current_time = datetime.utcnow().timestamp()
            remaining_time = int(exp - current_time)
            
            if remaining_time > 0:
                # Thêm token vào blacklist với thời gian còn lại
                TokenBlacklist.add_token(token, remaining_time)
                print(f"Token đã được blacklist trong {remaining_time} giây")
                return True
```
**Giải thích:**
- Decode token để lấy thời gian hết hạn
- Tính `remaining_time`: Thời gian token còn sống
- Chỉ blacklist token còn hiệu lực
- TTL của Redis = thời gian còn lại của token

```python
        return False
        
    except JWTError:
        # Token không hợp lệ, không cần blacklist
        return False
```

---

## 🔐 Multiple Token Types

### 1. Token Type Enum

```python
from enum import Enum

class TokenType(Enum):
    ACCESS = "access"
    REFRESH = "refresh"
    RESET_PASSWORD = "reset_password"
    EMAIL_VERIFICATION = "email_verification"
```
**Giải thích:**
- Định nghĩa các loại token khác nhau
- Mỗi loại có mục đích và thời gian sống khác nhau

### 2. Universal Token Creator

```python
def create_token(
    user_id: int,
    token_type: TokenType,
    expires_delta: Optional[timedelta] = None,
    extra_data: Optional[Dict[str, Any]] = None
) -> str:
    """
    Tạo token universal cho nhiều mục đích
    """
    # Thời gian hết hạn mặc định theo loại token
    if expires_delta is None:
        if token_type == TokenType.ACCESS:
            expires_delta = timedelta(minutes=30)
        elif token_type == TokenType.REFRESH:
            expires_delta = timedelta(days=7)
        elif token_type == TokenType.RESET_PASSWORD:
            expires_delta = timedelta(hours=1)  # Reset password: 1 giờ
        elif token_type == TokenType.EMAIL_VERIFICATION:
            expires_delta = timedelta(days=1)   # Verify email: 1 ngày
```
**Giải thích:**
- Mỗi loại token có thời gian sống phù hợp
- Reset password: Ngắn hạn (1 giờ) vì bảo mật cao
- Email verification: Trung hạn (1 ngày) vì user có thể check email muộn

```python
    expire = datetime.utcnow() + expires_delta
    
    # Base payload
    payload = {
        "sub": str(user_id),
        "type": token_type.value,
        "iat": datetime.utcnow(),
        "exp": expire,
        "jti": secrets.token_urlsafe(16)  # Unique ID
    }
    
    # Thêm data bổ sung nếu có
    if extra_data:
        payload.update(extra_data)
```
**Giải thích:**
- Base payload: Thông tin cơ bản cho mọi token
- `extra_data`: Cho phép thêm thông tin tùy chỉnh
- Ví dụ: Reset password token có thể chứa email

```python
    # Chọn secret key theo loại token
    if token_type == TokenType.REFRESH:
        secret_key = REFRESH_SECRET_KEY
    else:
        secret_key = SECRET_KEY
    
    return jwt.encode(payload, secret_key, algorithm=ALGORITHM)
```
**Giải thích:**
- Refresh token dùng secret key riêng
- Các token khác dùng secret key chung
- Tăng bảo mật: Compromise một key không ảnh hưởng tất cả

### 3. Universal Token Verifier

```python
def verify_token_by_type(
    token: str,
    expected_type: TokenType,
    check_blacklist: bool = True
) -> Optional[Dict[str, Any]]:
    """
    Verify token với kiểm tra loại token
    """
    try:
        # Kiểm tra blacklist nếu cần
        if check_blacklist and TokenBlacklist.is_blacklisted(token):
            return None
        
        # Chọn secret key theo loại token
        if expected_type == TokenType.REFRESH:
            secret_key = REFRESH_SECRET_KEY
        else:
            secret_key = SECRET_KEY
        
        # Decode token
        payload = jwt.decode(token, secret_key, algorithms=[ALGORITHM])
```
**Giải thích:**
- `expected_type`: Loại token mong đợi
- `check_blacklist`: Có kiểm tra blacklist không
- Chọn đúng secret key để decode

```python
        # Kiểm tra loại token
        token_type = payload.get("type")
        if token_type != expected_type.value:
            print(f"Token type mismatch. Expected: {expected_type.value}, Got: {token_type}")
            return None
```
**Giải thích:**
- Đảm bảo token đúng loại mong đợi
- Ngăn chặn việc dùng sai loại token
- Ví dụ: Không thể dùng access token để reset password

```python
        # Kiểm tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            return None
            
        return payload
        
    except JWTError:
        return None
```

---

## 🛡️ Advanced Security Features

### 1. Token Fingerprinting

```python
import hashlib

def create_token_fingerprint(user_agent: str, ip_address: str) -> str:
    """
    Tạo fingerprint từ user agent và IP
    """
    data = f"{user_agent}:{ip_address}"
    return hashlib.sha256(data.encode()).hexdigest()[:16]
```
**Giải thích:**
- Fingerprint: "Dấu vân tay" của client
- Kết hợp User-Agent và IP address
- Hash SHA256 và lấy 16 ký tự đầu

```python
def create_secure_token(
    user_id: int,
    user_agent: str,
    ip_address: str
) -> str:
    """
    Tạo token với fingerprint bảo mật
    """
    fingerprint = create_token_fingerprint(user_agent, ip_address)
    
    payload = {
        "sub": str(user_id),
        "type": "access",
        "fp": fingerprint,  # Fingerprint
        "iat": datetime.utcnow(),
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    
    return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
```
**Giải thích:**
- Thêm fingerprint vào token payload
- Token chỉ hợp lệ khi dùng từ cùng device/browser
- Ngăn chặn token hijacking

### 2. Verify với Fingerprint

```python
def verify_secure_token(
    token: str,
    user_agent: str,
    ip_address: str
) -> Optional[Dict[str, Any]]:
    """
    Verify token với kiểm tra fingerprint
    """
    payload = verify_token_with_blacklist(token)
    if not payload:
        return None
    
    # Kiểm tra fingerprint
    expected_fp = create_token_fingerprint(user_agent, ip_address)
    token_fp = payload.get("fp")
    
    if token_fp != expected_fp:
        print("Token fingerprint không khớp - có thể bị đánh cắp")
        return None
```
**Giải thích:**
- Tạo fingerprint từ request hiện tại
- So sánh với fingerprint trong token
- Nếu không khớp → Token có thể bị đánh cắp

```python
    return payload
```

---

## 🏭 Production Best Practices

### 1. Environment-based Configuration

```python
import os
from typing import Dict, Any

class JWTConfig:
    """
    Cấu hình JWT cho production
    """
    def __init__(self):
        # Lấy config từ environment variables
        self.SECRET_KEY = os.getenv("JWT_SECRET_KEY")
        self.REFRESH_SECRET_KEY = os.getenv("JWT_REFRESH_SECRET_KEY")
        self.ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")

        # Thời gian sống tokens
        self.ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
        self.REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

        # Validate required configs
        if not self.SECRET_KEY or not self.REFRESH_SECRET_KEY:
            raise ValueError("JWT secret keys must be set in environment variables")
```
**Giải thích:**
- Không hardcode secret keys trong code
- Sử dụng environment variables cho production
- Validate config khi khởi tạo để phát hiện lỗi sớm

### 2. Token Monitoring và Logging

```python
import logging
from datetime import datetime
from typing import Optional

# Setup logger
logger = logging.getLogger("jwt_monitor")

class TokenMonitor:
    """
    Monitor và log JWT token activities
    """

    @staticmethod
    def log_token_created(user_id: int, token_type: str, ip_address: str):
        """
        Log khi token được tạo
        """
        logger.info(f"Token created - User: {user_id}, Type: {token_type}, IP: {ip_address}")
```
**Giải thích:**
- Log mọi hoạt động liên quan đến token
- Giúp audit và debug security issues
- Track IP address để phát hiện suspicious activities

```python
    @staticmethod
    def log_token_verified(user_id: int, token_type: str, ip_address: str, success: bool):
        """
        Log khi token được verify
        """
        status = "SUCCESS" if success else "FAILED"
        logger.info(f"Token verify {status} - User: {user_id}, Type: {token_type}, IP: {ip_address}")

        # Alert nếu có nhiều failed attempts
        if not success:
            TokenMonitor._check_failed_attempts(user_id, ip_address)
```
**Giải thích:**
- Log cả success và failed attempts
- Monitor failed attempts để phát hiện brute force attacks
- Có thể trigger alerts hoặc temporary bans

```python
    @staticmethod
    def log_token_blacklisted(user_id: int, reason: str, ip_address: str):
        """
        Log khi token bị blacklist
        """
        logger.warning(f"Token blacklisted - User: {user_id}, Reason: {reason}, IP: {ip_address}")

    @staticmethod
    def _check_failed_attempts(user_id: int, ip_address: str):
        """
        Kiểm tra số lần failed attempts
        """
        # TODO: Implement rate limiting logic
        # Có thể ban IP hoặc user tạm thời
        pass
```

### 3. Complete Production JWT Class

```python
class ProductionJWT:
    """
    Production-ready JWT handler với full features
    """

    def __init__(self, config: JWTConfig):
        self.config = config
        self.blacklist = TokenBlacklist()
        self.monitor = TokenMonitor()

    def create_token_pair(
        self,
        user_id: int,
        username: str,
        role: str,
        ip_address: str,
        user_agent: str
    ) -> Dict[str, str]:
        """
        Tạo cặp access + refresh token
        """
        # Tạo fingerprint
        fingerprint = create_token_fingerprint(user_agent, ip_address)

        # Access token
        access_payload = {
            "sub": str(user_id),
            "username": username,
            "role": role,
            "type": "access",
            "fp": fingerprint,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(minutes=self.config.ACCESS_TOKEN_EXPIRE_MINUTES)
        }

        access_token = jwt.encode(access_payload, self.config.SECRET_KEY, algorithm=self.config.ALGORITHM)
```
**Giải thích:**
- Tạo cả access và refresh token cùng lúc
- Bao gồm fingerprint cho security
- Sử dụng config từ environment

```python
        # Refresh token
        refresh_payload = {
            "sub": str(user_id),
            "type": "refresh",
            "fp": fingerprint,
            "jti": secrets.token_urlsafe(32),
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(days=self.config.REFRESH_TOKEN_EXPIRE_DAYS)
        }

        refresh_token = jwt.encode(refresh_payload, self.config.REFRESH_SECRET_KEY, algorithm=self.config.ALGORITHM)

        # Log token creation
        self.monitor.log_token_created(user_id, "access", ip_address)
        self.monitor.log_token_created(user_id, "refresh", ip_address)

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": self.config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
```

```python
    def verify_access_token(
        self,
        token: str,
        ip_address: str,
        user_agent: str
    ) -> Optional[Dict[str, Any]]:
        """
        Verify access token với full security checks
        """
        try:
            # Kiểm tra blacklist
            if self.blacklist.is_blacklisted(token):
                self.monitor.log_token_verified(0, "access", ip_address, False)
                return None

            # Decode token
            payload = jwt.decode(token, self.config.SECRET_KEY, algorithms=[self.config.ALGORITHM])

            # Kiểm tra token type
            if payload.get("type") != "access":
                self.monitor.log_token_verified(0, "access", ip_address, False)
                return None

            # Kiểm tra fingerprint
            expected_fp = create_token_fingerprint(user_agent, ip_address)
            if payload.get("fp") != expected_fp:
                user_id = payload.get("sub", 0)
                self.monitor.log_token_verified(int(user_id), "access", ip_address, False)
                return None
```
**Giải thích:**
- Kiểm tra blacklist trước tiên
- Verify token type để tránh confusion attacks
- Kiểm tra fingerprint để phát hiện token theft

```python
            # Kiểm tra expiration
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
                user_id = payload.get("sub", 0)
                self.monitor.log_token_verified(int(user_id), "access", ip_address, False)
                return None

            # Success
            user_id = payload.get("sub", 0)
            self.monitor.log_token_verified(int(user_id), "access", ip_address, True)
            return payload

        except JWTError as e:
            self.monitor.log_token_verified(0, "access", ip_address, False)
            return None
```

```python
    def refresh_tokens(
        self,
        refresh_token: str,
        ip_address: str,
        user_agent: str
    ) -> Optional[Dict[str, str]]:
        """
        Refresh access token với rotation
        """
        # Verify refresh token
        payload = self.verify_refresh_token(refresh_token, ip_address, user_agent)
        if not payload:
            return None

        user_id = int(payload["sub"])

        # TODO: Lấy user info từ database
        # user = get_user_by_id(user_id)
        # if not user or not user.is_active:
        #     return None

        # Blacklist refresh token cũ (rotation)
        remaining_time = int(payload["exp"] - datetime.utcnow().timestamp())
        if remaining_time > 0:
            self.blacklist.add_token(refresh_token, remaining_time)
            self.monitor.log_token_blacklisted(user_id, "refresh_rotation", ip_address)

        # Tạo token pair mới
        return self.create_token_pair(
            user_id=user_id,
            username="user_from_db",  # Từ database
            role="user",              # Từ database
            ip_address=ip_address,
            user_agent=user_agent
        )
```
**Giải thích:**
- Refresh token rotation: Token cũ bị blacklist
- Lấy thông tin user mới nhất từ database
- Tạo cặp token hoàn toàn mới

### 4. FastAPI Integration

```python
from fastapi import Request, Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()
jwt_handler = ProductionJWT(JWTConfig())

def get_current_user_production(
    request: Request,
    credentials = Depends(security)
) -> Dict[str, Any]:
    """
    Production dependency để lấy current user
    """
    token = credentials.credentials
    ip_address = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "")

    # Verify token
    payload = jwt_handler.verify_access_token(token, ip_address, user_agent)

    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
```
**Giải thích:**
- Lấy IP address và User-Agent từ request
- Sử dụng production JWT handler
- Tự động log mọi verification attempts

```python
    # Lấy user info từ payload
    user_id = payload.get("sub")
    username = payload.get("username")
    role = payload.get("role")

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )

    return {
        "user_id": int(user_id),
        "username": username,
        "role": role,
        "ip_address": ip_address
    }
```

### 5. Environment Variables (.env)

```bash
# JWT Configuration
JWT_SECRET_KEY=your-super-secret-key-minimum-32-characters-long-for-production
JWT_REFRESH_SECRET_KEY=different-secret-key-for-refresh-tokens-also-32-chars-min
JWT_ALGORITHM=HS256

# Token Expiration
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Redis Configuration (for blacklist)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# Logging
LOG_LEVEL=INFO
LOG_FILE=/var/log/auth-service/jwt.log
```
**Giải thích:**
- Tất cả sensitive data trong environment variables
- Separate keys cho access và refresh tokens
- Redis config cho token blacklist
- Logging configuration

---

## 📊 Monitoring và Metrics

### 1. Token Metrics

```python
from collections import defaultdict
from datetime import datetime, timedelta

class TokenMetrics:
    """
    Thu thập metrics về JWT tokens
    """

    def __init__(self):
        self.token_created_count = defaultdict(int)
        self.token_verified_count = defaultdict(int)
        self.failed_verification_count = defaultdict(int)
        self.blacklisted_tokens_count = 0

    def record_token_created(self, token_type: str):
        """Record token creation"""
        self.token_created_count[token_type] += 1

    def record_token_verified(self, token_type: str, success: bool):
        """Record token verification"""
        if success:
            self.token_verified_count[token_type] += 1
        else:
            self.failed_verification_count[token_type] += 1

    def get_metrics(self) -> Dict[str, Any]:
        """Lấy tất cả metrics"""
        return {
            "tokens_created": dict(self.token_created_count),
            "tokens_verified": dict(self.token_verified_count),
            "failed_verifications": dict(self.failed_verification_count),
            "blacklisted_tokens": self.blacklisted_tokens_count,
            "timestamp": datetime.utcnow().isoformat()
        }
```
**Giải thích:**
- Track số lượng tokens được tạo và verify
- Monitor failed attempts để phát hiện attacks
- Metrics có thể export sang monitoring systems (Prometheus, etc.)

---

## 🔒 Security Checklist

### Production Security Requirements:

- ✅ **Strong Secret Keys**: Minimum 32 characters, random generated
- ✅ **Environment Variables**: No hardcoded secrets in code
- ✅ **Token Expiration**: Short-lived access tokens (15-30 minutes)
- ✅ **Refresh Token Rotation**: New refresh token on each refresh
- ✅ **Token Blacklist**: Ability to revoke tokens before expiration
- ✅ **Fingerprinting**: Bind tokens to specific devices/browsers
- ✅ **Rate Limiting**: Prevent brute force attacks
- ✅ **Logging & Monitoring**: Track all token activities
- ✅ **HTTPS Only**: Never send tokens over HTTP
- ✅ **Secure Storage**: Client-side token storage best practices

### Common Security Mistakes:

- ❌ **Long-lived access tokens**: Increases attack window
- ❌ **No token rotation**: Compromised refresh tokens stay valid
- ❌ **Client-side secret keys**: Never put secrets in frontend
- ❌ **No fingerprinting**: Tokens can be used from any device
- ❌ **No monitoring**: Can't detect security breaches
- ❌ **Weak secret keys**: Easy to brute force
- ❌ **No blacklist mechanism**: Can't revoke compromised tokens

---

## 🎯 Tóm tắt

### JWT Advanced Features đã học:

1. **Refresh Token System**: Tự động gia hạn access tokens
2. **Token Blacklist**: Thu hồi tokens trước khi hết hạn
3. **Multiple Token Types**: Phân loại tokens theo mục đích
4. **Token Fingerprinting**: Bind tokens với devices
5. **Production Configuration**: Environment-based setup
6. **Monitoring & Logging**: Track security events
7. **Metrics Collection**: Performance và security metrics

### Next Steps:

- 🔄 **OAuth2 Integration**: Social login (Google, Facebook)
- 🔐 **2FA Implementation**: Two-factor authentication
- 📱 **Mobile JWT**: Special considerations for mobile apps
- 🌐 **Microservices JWT**: Token sharing across services
- 🛡️ **Advanced Attacks**: CSRF, XSS protection with JWT

---

*Với kiến thức này, bạn đã có thể xây dựng một JWT system production-ready với đầy đủ security features!*
