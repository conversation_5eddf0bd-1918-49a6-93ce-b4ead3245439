# 🚀 Tutorial Step-by-Step: Xây dựng Authentication System

## 📋 Mục tiêu
Xây dựng một hệ thống authentication hoàn chỉnh với:
- ✅ User registration & login
- ✅ JWT token authentication
- ✅ Protected routes với role-based access
- ✅ Middleware cho security
- ✅ Rate limiting và logging

---

## 🛠️ Bước 1: Setup Project Structure

### 1.1 T<PERSON>o cấu trúc thư mục
```
Auth_services/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── security.py
│   │   └── deps.py
│   ├── models/
│   │   ├── __init__.py
│   │   └── user.py
│   ├── schemas/
│   │   ├── __init__.py
│   │   └── auth.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   └── auth.py
│   ├── middleware/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── rate_limit.py
│   │   └── logging.py
│   └── database.py
├── requirements.txt
├── run.py
└── .env
```

### 1.2 Cập nhật requirements.txt
```txt
# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.21
alembic==1.12.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Configuration
pydantic==2.4.2
pydantic-settings==2.0.3
python-dotenv==1.0.0

# Development
pytest==7.4.2
httpx==0.25.0
```

### 1.3 Cài đặt dependencies
```bash
pip install -r requirements.txt
```

---

## 🔧 Bước 2: Configuration Setup

### 2.1 Tạo file .env
```bash
# JWT Settings
SECRET_KEY=your-super-secret-key-change-in-production-minimum-32-characters-long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_URL=sqlite:///./auth.db

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
```

### 2.2 Tạo app/core/config.py
```python
from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings):
    # JWT Settings
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Database
    DATABASE_URL: str
    
    # CORS
    ALLOWED_ORIGINS: List[str] = []
    
    class Config:
        env_file = ".env"
        
    def get_allowed_origins(self) -> List[str]:
        if isinstance(self.ALLOWED_ORIGINS, str):
            return [origin.strip() for origin in self.ALLOWED_ORIGINS.split(",")]
        return self.ALLOWED_ORIGINS

settings = Settings()
```

---

## 🗄️ Bước 3: Database Setup

### 3.1 Tạo app/database.py
```python
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .core.config import settings

# Tạo database engine
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class cho models
Base = declarative_base()

def get_db():
    """
    Dependency để lấy database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

### 3.2 Tạo app/models/user.py
```python
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.sql import func
from ..database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    
    # Role và status
    role = Column(String(20), default="user")  # user, admin, moderator
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def has_permission(self, permission: str) -> bool:
        """Kiểm tra user có permission không"""
        role_permissions = {
            "admin": ["read", "write", "delete", "manage_users"],
            "moderator": ["read", "write", "moderate"],
            "user": ["read", "write_own"]
        }
        return permission in role_permissions.get(self.role, [])
    
    def is_admin(self) -> bool:
        return self.role == "admin"
```

---

## 🔐 Bước 4: Security Implementation

### 4.1 Tạo app/core/security.py
```python
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from .config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash password"""
    return pwd_context.hash(password)

def create_access_token(subject: str, expires_delta: Optional[timedelta] = None) -> str:
    """Tạo JWT access token"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode = {
        "exp": expire,
        "iat": datetime.utcnow(),
        "sub": str(subject)
    }
    
    return jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

def verify_token(token: str) -> Optional[dict]:
    """Verify JWT token"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            return None
        return payload
    except JWTError:
        return None
```

### 4.2 Tạo app/schemas/auth.py
```python
from typing import Optional
from pydantic import BaseModel, EmailStr

class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class UserResponse(UserBase):
    id: int
    role: str
    is_active: bool
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
```

---

## 🛡️ Bước 5: Dependencies và Authentication

### 5.1 Tạo app/core/deps.py
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from .security import verify_token
from ..models.user import User
from ..database import get_db

security = HTTPBearer()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Lấy current user từ JWT token"""
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )
    
    user = db.query(User).filter(User.id == int(user_id)).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return user

def get_current_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """Chỉ admin mới truy cập được"""
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user
```

---

## 🌐 Bước 6: API Endpoints

### 6.1 Tạo app/api/v1/auth.py
```python
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ...core.config import settings
from ...core.security import verify_password, get_password_hash, create_access_token
from ...core.deps import get_current_user, get_current_admin_user
from ...models.user import User
from ...schemas.auth import UserCreate, UserResponse, Token, UserLogin
from ...database import get_db

router = APIRouter(prefix="/auth", tags=["Authentication"])

@router.post("/register", response_model=UserResponse)
def register(user_data: UserCreate, db: Session = Depends(get_db)):
    """Đăng ký user mới"""
    # Kiểm tra user đã tồn tại
    existing_user = db.query(User).filter(
        (User.username == user_data.username) | (User.email == user_data.email)
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already registered"
        )
    
    # Tạo user mới
    hashed_password = get_password_hash(user_data.password)
    db_user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        hashed_password=hashed_password
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

@router.post("/login", response_model=Token)
def login(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """Đăng nhập"""
    user = db.query(User).filter(User.username == user_credentials.username).first()
    
    if not user or not verify_password(user_credentials.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    # Tạo access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(subject=user.id, expires_delta=access_token_expires)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@router.get("/me", response_model=UserResponse)
def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Lấy thông tin user hiện tại"""
    return current_user

@router.get("/admin-only")
def admin_only_endpoint(current_user: User = Depends(get_current_admin_user)):
    """Endpoint chỉ admin truy cập được"""
    return {
        "message": f"Hello admin {current_user.username}!",
        "admin_data": "This is sensitive admin data"
    }
```

### 6.2 Tạo app/api/v1/__init__.py
```python
from fastapi import APIRouter
from .auth import router as auth_router

api_router = APIRouter()
api_router.include_router(auth_router)
```

---

## 🚀 Bước 7: Main Application

### 7.1 Cập nhật app/main.py
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .core.config import settings
from .api.v1 import api_router
from .database import engine, Base

# Tạo database tables
Base.metadata.create_all(bind=engine)

# Tạo FastAPI app
app = FastAPI(
    title="Auth Service API",
    description="Microservice cho xác thực người dùng",
    version="1.0.0"
)

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_allowed_origins(),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
def root():
    return {"message": "Auth Service API is running!"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}
```

---

## 🧪 Bước 8: Testing

### 8.1 Chạy server
```bash
python run.py
```

### 8.2 Test với curl

**1. Đăng ký user:**
```bash
curl -X POST "http://localhost:8001/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpassword123",
    "full_name": "Test User"
  }'
```

**2. Đăng nhập:**
```bash
curl -X POST "http://localhost:8001/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpassword123"
  }'
```

**3. Truy cập protected endpoint:**
```bash
curl -X GET "http://localhost:8001/api/v1/auth/me" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 8.3 Test với Swagger UI
Truy cập: `http://localhost:8001/docs`

---

## 🎯 Bước 9: Next Steps

### 9.1 Thêm Middleware (Optional)
- Rate limiting
- Request logging
- Security headers

### 9.2 Advanced Features
- Refresh tokens
- Password reset
- Email verification
- 2FA authentication

### 9.3 Production Considerations
- Environment-specific configs
- Database migrations với Alembic
- Docker containerization
- CI/CD pipeline

---

## ✅ Checklist Hoàn thành

- [ ] ✅ Project structure setup
- [ ] ✅ Configuration management
- [ ] ✅ Database models
- [ ] ✅ JWT authentication
- [ ] ✅ API endpoints
- [ ] ✅ Protected routes
- [ ] ✅ Role-based access
- [ ] ✅ Testing endpoints
- [ ] 🔄 Middleware implementation
- [ ] 🔄 Advanced security features

**🎉 Chúc mừng! Bạn đã xây dựng thành công một Authentication System hoàn chỉnh!**
