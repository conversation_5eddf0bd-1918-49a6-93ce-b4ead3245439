# 🔧 Middleware và Advanced Security

## 📋 Mục lục
1. [Custom Middleware](#custom-middleware)
2. [Rate Limiting](#rate-limiting)
3. [CORS Configuration](#cors-configuration)
4. [Request Logging](#request-logging)
5. [Security Headers](#security-headers)

---

## 🛠️ Custom Middleware

### 1. Authentication Middleware (app/middleware/auth.py)

```python
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable
import time

from ..core.security import verify_token
from ..models.user import User
from ..database import SessionLocal

class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    Middleware để tự động xác thực user cho tất cả requests
    """
    
    def __init__(self, app, exclude_paths: list = None):
        """
        Args:
            app: FastAPI application
            exclude_paths: Danh sách paths không cần authentication
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/docs", "/redoc", "/openapi.json",
            "/auth/login", "/auth/register",
            "/health", "/"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable):
        """
        Xử lý mỗi request trước khi đến endpoint
        
        Args:
            request: HTTP request
            call_next: Function để gọi endpoint tiếp theo
        
        Returns:
            Response: HTTP response
        """
        # Bỏ qua authentication cho một số paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Lấy Authorization header
        authorization = request.headers.get("Authorization")
        
        if not authorization:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Authorization header missing"}
            )
        
        # Kiểm tra Bearer token format
        try:
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                raise ValueError("Invalid scheme")
        except ValueError:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid authorization header format"}
            )
        
        # Verify token
        payload = verify_token(token)
        if not payload:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid or expired token"}
            )
        
        # Lấy user từ database
        user_id = payload.get("sub")
        if not user_id:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid token payload"}
            )
        
        # Thêm user info vào request state
        db = SessionLocal()
        try:
            user = db.query(User).filter(User.id == int(user_id)).first()
            if not user or not user.is_active:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "User not found or inactive"}
                )
            
            # Lưu user info vào request để sử dụng trong endpoints
            request.state.current_user = user
            
        finally:
            db.close()
        
        # Tiếp tục xử lý request
        response = await call_next(request)
        return response
```

### 2. Rate Limiting Middleware (app/middleware/rate_limit.py)

```python
import time
from collections import defaultdict, deque
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Dict, Deque

class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware để giới hạn số lượng requests từ mỗi IP
    """
    
    def __init__(
        self, 
        app, 
        calls: int = 100,      # Số requests tối đa
        period: int = 60       # Trong khoảng thời gian (seconds)
    ):
        """
        Args:
            app: FastAPI application
            calls: Số lượng calls tối đa
            period: Thời gian tính bằng seconds
        """
        super().__init__(app)
        self.calls = calls
        self.period = period
        # Dictionary lưu trữ requests của mỗi IP
        self.requests: Dict[str, Deque[float]] = defaultdict(deque)
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Lấy IP address của client
        
        Args:
            request: HTTP request
        
        Returns:
            str: Client IP address
        """
        # Kiểm tra X-Forwarded-For header (nếu có proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # Kiểm tra X-Real-IP header
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to client host
        return request.client.host if request.client else "unknown"
    
    def _is_rate_limited(self, client_ip: str) -> bool:
        """
        Kiểm tra IP có bị rate limit không
        
        Args:
            client_ip: IP address của client
        
        Returns:
            bool: True nếu bị rate limit
        """
        now = time.time()
        
        # Lấy danh sách requests của IP này
        requests = self.requests[client_ip]
        
        # Xóa các requests cũ (ngoài time window)
        while requests and requests[0] <= now - self.period:
            requests.popleft()
        
        # Kiểm tra số lượng requests trong time window
        if len(requests) >= self.calls:
            return True
        
        # Thêm request hiện tại
        requests.append(now)
        return False
    
    async def dispatch(self, request: Request, call_next):
        """
        Xử lý rate limiting cho mỗi request
        """
        client_ip = self._get_client_ip(request)
        
        # Kiểm tra rate limit
        if self._is_rate_limited(client_ip):
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "detail": f"Rate limit exceeded. Max {self.calls} requests per {self.period} seconds",
                    "retry_after": self.period
                },
                headers={"Retry-After": str(self.period)}
            )
        
        # Tiếp tục xử lý request
        response = await call_next(request)
        
        # Thêm rate limit headers vào response
        remaining = self.calls - len(self.requests[client_ip])
        response.headers["X-RateLimit-Limit"] = str(self.calls)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time() + self.period))
        
        return response
```

### 3. Request Logging Middleware (app/middleware/logging.py)

```python
import time
import logging
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import json

# Setup logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware để log tất cả requests và responses
    """
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """
        Log request và response details
        """
        start_time = time.time()
        
        # Log request
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "client_ip": request.client.host if request.client else "unknown",
            "user_agent": request.headers.get("user-agent", ""),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
        }
        
        # Lấy user info nếu có
        if hasattr(request.state, "current_user"):
            request_info["user_id"] = request.state.current_user.id
            request_info["username"] = request.state.current_user.username
        
        logger.info(f"Request: {json.dumps(request_info)}")
        
        # Xử lý request
        try:
            response = await call_next(request)
            
            # Tính thời gian xử lý
            process_time = time.time() - start_time
            
            # Log response
            response_info = {
                "status_code": response.status_code,
                "process_time": f"{process_time:.4f}s",
                "response_size": response.headers.get("content-length", "unknown")
            }
            
            logger.info(f"Response: {json.dumps(response_info)}")
            
            # Thêm process time vào header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # Log lỗi
            process_time = time.time() - start_time
            error_info = {
                "error": str(e),
                "process_time": f"{process_time:.4f}s",
                "status": "error"
            }
            
            logger.error(f"Error: {json.dumps(error_info)}")
            raise
```

---

## 🔒 Security Headers Middleware

### Security Headers (app/middleware/security.py)

```python
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware để thêm các security headers
    """
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Security headers
        security_headers = {
            # Prevent clickjacking
            "X-Frame-Options": "DENY",
            
            # Prevent MIME type sniffing
            "X-Content-Type-Options": "nosniff",
            
            # XSS Protection
            "X-XSS-Protection": "1; mode=block",
            
            # Referrer Policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Content Security Policy
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self'; "
                "connect-src 'self'; "
                "frame-ancestors 'none';"
            ),
            
            # HSTS (HTTPS Strict Transport Security)
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            
            # Permissions Policy
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            )
        }
        
        # Thêm tất cả security headers
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response
```

---

## 🌐 CORS Configuration

### CORS Middleware Setup (app/main.py)

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .middleware.auth import AuthenticationMiddleware
from .middleware.rate_limit import RateLimitMiddleware
from .middleware.logging import RequestLoggingMiddleware
from .middleware.security import SecurityHeadersMiddleware

app = FastAPI(
    title="Auth Service API",
    description="Microservice cho xác thực người dùng",
    version="1.0.0"
)

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",    # React dev server
        "http://localhost:8080",    # Vue dev server
        "https://yourdomain.com",   # Production domain
    ],
    allow_credentials=True,         # Cho phép gửi cookies
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Authorization",
        "Content-Type",
        "X-Requested-With",
        "Accept",
        "Origin",
        "User-Agent",
        "DNT",
        "Cache-Control",
        "X-Mx-ReqToken",
        "Keep-Alive",
        "X-Requested-With",
        "If-Modified-Since",
    ],
    expose_headers=[
        "X-Process-Time",
        "X-RateLimit-Limit",
        "X-RateLimit-Remaining",
        "X-RateLimit-Reset"
    ]
)

# Security Headers Middleware
app.add_middleware(SecurityHeadersMiddleware)

# Request Logging Middleware
app.add_middleware(RequestLoggingMiddleware)

# Rate Limiting Middleware
app.add_middleware(
    RateLimitMiddleware,
    calls=100,      # 100 requests
    period=60       # per minute
)

# Authentication Middleware (optional - nếu muốn global auth)
# app.add_middleware(
#     AuthenticationMiddleware,
#     exclude_paths=["/docs", "/redoc", "/openapi.json", "/auth/login", "/auth/register"]
# )

# Include routers
from .api.v1 import api_router as api_v1_router
app.include_router(api_v1_router, prefix="/api/v1")
```

---

## 🔧 Advanced Security Features

### 1. Token Blacklist (app/core/token_blacklist.py)

```python
import redis
from typing import Optional
from .config import settings

# Redis client cho token blacklist
redis_client = redis.Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    db=settings.REDIS_DB,
    decode_responses=True
)

class TokenBlacklist:
    """
    Quản lý blacklist tokens (cho logout functionality)
    """
    
    @staticmethod
    def add_token(token: str, expires_in: int):
        """
        Thêm token vào blacklist
        
        Args:
            token: JWT token
            expires_in: Thời gian hết hạn (seconds)
        """
        redis_client.setex(f"blacklist:{token}", expires_in, "1")
    
    @staticmethod
    def is_blacklisted(token: str) -> bool:
        """
        Kiểm tra token có trong blacklist không
        
        Args:
            token: JWT token
        
        Returns:
            bool: True nếu token bị blacklist
        """
        return redis_client.exists(f"blacklist:{token}") > 0
    
    @staticmethod
    def remove_token(token: str):
        """
        Xóa token khỏi blacklist
        
        Args:
            token: JWT token
        """
        redis_client.delete(f"blacklist:{token}")
```

### 2. Enhanced Token Verification

```python
# Cập nhật app/core/security.py
def verify_token(token: str) -> Optional[dict]:
    """
    Enhanced token verification với blacklist check
    """
    try:
        # Kiểm tra token có trong blacklist không
        if TokenBlacklist.is_blacklisted(token):
            return None
        
        # Decode JWT token
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        
        # Kiểm tra expiration
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            return None
            
        return payload
        
    except JWTError as e:
        print(f"JWT Error: {e}")
        return None
    except Exception as e:
        print(f"Token verification error: {e}")
        return None
```

### 3. Logout Endpoint

```python
# Thêm vào app/api/auth.py
@router.post("/logout")
def logout(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """
    Logout user và blacklist token
    """
    # Lấy token từ Authorization header
    authorization = request.headers.get("Authorization")
    if authorization:
        try:
            scheme, token = authorization.split()
            if scheme.lower() == "bearer":
                # Thêm token vào blacklist
                # Token sẽ hết hạn sau ACCESS_TOKEN_EXPIRE_MINUTES
                TokenBlacklist.add_token(
                    token, 
                    settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
                )
        except ValueError:
            pass
    
    return {"message": "Successfully logged out"}
```

---

## 📚 Best Practices

### 1. Environment Variables (.env)

```bash
# JWT Settings
SECRET_KEY=your-super-secret-key-change-in-production-minimum-32-characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database
DATABASE_URL=postgresql://user:password@localhost/auth_db

# Redis (for token blacklist)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# CORS
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Rate Limiting
RATE_LIMIT_CALLS=100
RATE_LIMIT_PERIOD=60
```

### 2. Security Checklist

- ✅ **Strong SECRET_KEY** (minimum 32 characters)
- ✅ **HTTPS in production** (never HTTP for auth)
- ✅ **Token expiration** (short-lived access tokens)
- ✅ **Rate limiting** (prevent brute force)
- ✅ **Input validation** (Pydantic schemas)
- ✅ **Password hashing** (bcrypt with salt)
- ✅ **CORS configuration** (restrictive origins)
- ✅ **Security headers** (XSS, clickjacking protection)
- ✅ **Request logging** (audit trail)
- ✅ **Token blacklist** (logout functionality)

---

*Tài liệu này cung cấp foundation hoàn chỉnh cho Authentication & Authorization system. Tiếp theo bạn có thể implement các features như 2FA, OAuth integration, và advanced permission systems.*
