# 🎫 JWT Cơ Bản - Hướng dẫn từng bước

## 📋 <PERSON>ục lục
1. [JWT là gì?](#jwt-là-gì)
2. [Cài đặt và Setup](#cài-đặt-và-setup)
3. [Tạo JWT Token](#tạo-jwt-token)
4. [Verify JWT Token](#verify-jwt-token)
5. [Thực hành cơ bản](#thực-hành-cơ-bản)

---

## 🎯 JWT là gì?

**JWT (JSON Web Token)** là một chuẩn mở để truyền thông tin an toàn giữa các bên dưới dạng JSON object.

### Cấu trúc JWT:
```
xxxxx.yyyyy.zzzzz
Header.Payload.Signature
```

**Ví dụ JWT thực tế:**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

### 3 phần của JWT:

**1. Header (Phần đầu):**
```json
{
  "alg": "HS256",    // Thuật toán mã hóa sử dụng
  "typ": "JWT"       // Loại token
}
```

**2. Payload (Dữ liệu):**
```json
{
  "sub": "1234567890",           // Subject - ID của user
  "name": "John Doe",            // Tên user
  "iat": 1516239022,             // Issued At - thời gian tạo token
  "exp": 1516242622              // Expiration - thời gian hết hạn
}
```

**3. Signature (Chữ ký):**
```
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret_key
)
```

---

## 🛠️ Cài đặt và Setup

### 1. Cài đặt thư viện cần thiết

```bash
pip install python-jose[cryptography] passlib[bcrypt]
```

**Giải thích:**
- `python-jose`: Thư viện để tạo và verify JWT tokens
- `cryptography`: Thư viện mã hóa cho JWT
- `passlib[bcrypt]`: Thư viện hash password an toàn

### 2. Import các thư viện

```python
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
```

**Giải thích từng import:**
- `datetime, timedelta`: Để xử lý thời gian tạo và hết hạn token
- `Optional`: Type hint cho Python, cho phép giá trị None
- `JWTError, jwt`: Từ thư viện jose để xử lý JWT

### 3. Cấu hình cơ bản

```python
# Cấu hình JWT
SECRET_KEY = "your-super-secret-key-minimum-32-characters-long"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
```

**Giải thích từng biến:**
- `SECRET_KEY`: Khóa bí mật để ký JWT (phải giữ bí mật tuyệt đối)
- `ALGORITHM`: Thuật toán mã hóa (HS256 là phổ biến nhất)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: Token sẽ hết hạn sau 30 phút

---

## 🔐 Tạo JWT Token

### Hàm tạo token cơ bản:

```python
def create_access_token(user_id: int, username: str) -> str:
    """
    Tạo JWT access token cho user
    
    Args:
        user_id: ID của user trong database
        username: Tên đăng nhập của user
    
    Returns:
        str: JWT token string
    """
    # Tính thời gian hết hạn
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
```
**Giải thích:**
- `datetime.utcnow()`: Lấy thời gian hiện tại theo UTC
- `timedelta(minutes=30)`: Thêm 30 phút vào thời gian hiện tại
- Token sẽ hết hạn sau 30 phút kể từ khi tạo

```python
    # Tạo payload (dữ liệu) của token
    payload = {
        "sub": str(user_id),        # Subject - ID user (phải là string)
        "username": username,       # Tên user để dễ debug
        "iat": datetime.utcnow(),   # Issued At - thời gian tạo token
        "exp": expire               # Expiration - thời gian hết hạn
    }
```
**Giải thích từng field trong payload:**
- `sub` (Subject): Chứa ID của user, JWT standard yêu cầu phải là string
- `username`: Thông tin bổ sung, giúp debug và log
- `iat` (Issued At): Thời gian tạo token, dùng để tracking
- `exp` (Expiration): Thời gian hết hạn, JWT sẽ tự động kiểm tra

```python
    # Tạo JWT token
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    return token
```
**Giải thích:**
- `jwt.encode()`: Hàm tạo JWT từ payload, secret key và algorithm
- Kết quả là một string JWT có thể gửi cho client

### Ví dụ sử dụng:

```python
# Tạo token cho user
user_token = create_access_token(user_id=123, username="john_doe")
print(f"Token: {user_token}")
```

---

## 🔍 Verify JWT Token

### Hàm verify token cơ bản:

```python
def verify_token(token: str) -> Optional[dict]:
    """
    Kiểm tra và giải mã JWT token
    
    Args:
        token: JWT token string từ client
    
    Returns:
        dict: Payload của token nếu hợp lệ, None nếu không hợp lệ
    """
    try:
        # Giải mã JWT token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
```
**Giải thích:**
- `jwt.decode()`: Hàm giải mã JWT token
- Cần cung cấp: token, secret key, và algorithm để verify
- Nếu token bị thay đổi hoặc sai secret key → sẽ raise JWTError

```python
        # Kiểm tra token có hết hạn chưa
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            print("Token đã hết hạn")
            return None
```
**Giải thích:**
- `payload.get("exp")`: Lấy thời gian hết hạn từ payload
- `datetime.fromtimestamp(exp)`: Chuyển timestamp thành datetime object
- So sánh với thời gian hiện tại để kiểm tra hết hạn

```python
        return payload
        
    except JWTError as e:
        print(f"Lỗi JWT: {e}")
        return None
```
**Giải thích:**
- `JWTError`: Exception được raise khi token không hợp lệ
- Có thể do: token bị sửa đổi, sai secret key, format không đúng
- Return None để báo hiệu token không hợp lệ

```python
    except Exception as e:
        print(f"Lỗi không xác định: {e}")
        return None
```
**Giải thích:**
- Catch tất cả các lỗi khác có thể xảy ra
- Ví dụ: lỗi network, lỗi database, etc.
- Luôn return None để đảm bảo security

---

## 🧪 Thực hành cơ bản

### File hoàn chỉnh: `jwt_basic_example.py`

```python
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt

# Cấu hình
SECRET_KEY = "your-super-secret-key-minimum-32-characters-long"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(user_id: int, username: str) -> str:
    """Tạo JWT token"""
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    payload = {
        "sub": str(user_id),
        "username": username,
        "iat": datetime.utcnow(),
        "exp": expire
    }
    
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    return token

def verify_token(token: str) -> Optional[dict]:
    """Verify JWT token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            return None
            
        return payload
        
    except JWTError:
        return None
    except Exception:
        return None

# Test functions
if __name__ == "__main__":
    # Tạo token
    token = create_access_token(user_id=123, username="test_user")
    print(f"Token được tạo: {token}")
    
    # Verify token
    payload = verify_token(token)
    if payload:
        print(f"Token hợp lệ! User ID: {payload['sub']}, Username: {payload['username']}")
    else:
        print("Token không hợp lệ!")
```

### Chạy test:

```bash
python jwt_basic_example.py
```

**Kết quả mong đợi:**
```
Token được tạo: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Token hợp lệ! User ID: 123, Username: test_user
```

---

## 🔧 Sử dụng trong FastAPI

### 1. Tạo dependency để lấy user từ token:

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

def get_current_user(token: str = Depends(security)):
    """
    Dependency để lấy thông tin user từ JWT token
    """
    # Lấy token từ Authorization header
    credentials = token.credentials
```
**Giải thích:**
- `HTTPBearer()`: FastAPI security scheme cho Bearer token
- Tự động lấy token từ header "Authorization: Bearer <token>"
- `credentials.credentials`: Chứa token string

```python
    # Verify token
    payload = verify_token(credentials)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
```
**Giải thích:**
- Gọi hàm `verify_token()` để kiểm tra token
- Nếu token không hợp lệ (None) → raise HTTPException
- Status code 401: Unauthorized (chưa xác thực)
- Header "WWW-Authenticate": Báo cho client biết cần Bearer token

```python
    # Lấy user ID từ payload
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )
```
**Giải thích:**
- `payload.get("sub")`: Lấy user ID từ token payload
- "sub" là field standard của JWT chứa subject (user ID)
- Nếu không có user_id → token không đúng format → raise exception

```python
    return {"user_id": int(user_id), "username": payload.get("username")}
```
**Giải thích:**
- Convert user_id từ string về int (vì JWT lưu dưới dạng string)
- Return dictionary chứa thông tin user để sử dụng trong endpoint

### 2. Sử dụng trong endpoint:

```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/protected")
def protected_route(current_user: dict = Depends(get_current_user)):
    """
    Route được bảo vệ bởi JWT token
    """
    return {
        "message": f"Hello {current_user['username']}!",
        "user_id": current_user["user_id"],
        "protected_data": "This is sensitive information"
    }
```
**Giải thích:**
- `Depends(get_current_user)`: FastAPI sẽ tự động gọi function này
- Nếu token hợp lệ → current_user chứa thông tin user
- Nếu token không hợp lệ → FastAPI tự động return 401 error

---

## 📚 Tóm tắt

### Quy trình JWT cơ bản:

1. **Tạo token:** `create_access_token()` → JWT string
2. **Client lưu token:** Trong localStorage hoặc cookie
3. **Gửi token:** Header "Authorization: Bearer <token>"
4. **Server verify:** `verify_token()` → payload hoặc None
5. **Xử lý kết quả:** Cho phép truy cập hoặc từ chối

### Các lỗi thường gặp:

- ❌ **Secret key quá ngắn:** Minimum 32 characters
- ❌ **Token hết hạn:** Kiểm tra exp field
- ❌ **Sai algorithm:** Phải match khi encode/decode
- ❌ **Token bị sửa đổi:** Signature không khớp

### Best practices:

- ✅ **Secret key mạnh:** Random, minimum 32 chars
- ✅ **Token ngắn hạn:** 15-30 phút cho access token
- ✅ **Luôn verify:** Không tin tưởng token từ client
- ✅ **Handle errors:** Graceful error handling

---

*Tiếp theo, hãy đọc `jwt_advanced_guide.md` để tìm hiểu về refresh tokens, token blacklist, và advanced security features!*
