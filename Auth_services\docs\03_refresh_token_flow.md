# 🔄 Refresh Token Flow - Tránh login lại liên tục

## 📋 Mục tiêu
- ✅ Hiểu tại sao cần Refresh Token
- ✅ Implement Refresh Token system
- ✅ Auto-refresh Access Token khi hết hạn
- ✅ Token Rotation để tăng bảo mật

---

## 🤔 Tại sao cần Refresh Token?

### Vấn đề với chỉ có Access Token:

**Scenario 1: Access Token ngắn hạn (15-30 phút)**
- ✅ Bảo mật cao: Token bị đánh cắp chỉ sử dụng được ít thời gian
- ❌ UX kém: User phải đăng nhập lại liên tục

**Scenario 2: Access Token dài hạn (nhiều giờ/ngày)**
- ✅ UX tốt: User không cần đăng nhập lại
- ❌ B<PERSON><PERSON> mật thấp: Token bị đánh cắp có thể sử dụng lâu

### Giải pháp Refresh Token:

```
Access Token: 30 phút (ngắn hạn, bảo mật cao)
Refresh Token: 7 ngày (dài hạn, chỉ dùng để lấy Access Token mới)
```

**Luồng hoạt động:**
1. User login → Nhận cả Access Token và Refresh Token
2. Gửi request với Access Token
3. Access Token hết hạn → Dùng Refresh Token để lấy Access Token mới
4. Tiếp tục sử dụng Access Token mới

---

## 🔧 Bước 1: Cập nhật JWT Utils

### 1.1 Cập nhật `app/core/jwt_utils.py`

```python
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from fastapi import HTTPException, status
import secrets

# Cấu hình JWT nâng cao
SECRET_KEY = "your-super-secret-key-change-in-production-32-chars-minimum"
REFRESH_SECRET_KEY = "different-secret-key-for-refresh-tokens-also-32-chars"  # Key riêng cho refresh token
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30    # Access token: 30 phút
REFRESH_TOKEN_EXPIRE_DAYS = 7       # Refresh token: 7 ngày

def create_access_token(user_id: int, username: str, role: str = "user") -> str:
    """
    Tạo Access Token (ngắn hạn)
    """
    expire_time = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    payload = {
        "sub": str(user_id),
        "username": username,
        "role": role,
        "type": "access",              # Đánh dấu loại token
        "iat": datetime.utcnow(),
        "exp": expire_time
    }
    
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    print(f"✅ Access token created for {username} (expires in {ACCESS_TOKEN_EXPIRE_MINUTES} minutes)")
    return token
```
**Giải thích:**
- `REFRESH_SECRET_KEY`: Key riêng biệt cho refresh token (tăng bảo mật)
- `type: "access"`: Phân biệt access token và refresh token
- Access token có thời gian sống ngắn (30 phút)

```python
def create_refresh_token(user_id: int) -> str:
    """
    Tạo Refresh Token (dài hạn)
    
    Args:
        user_id: ID của user
    
    Returns:
        str: Refresh token string
    """
    expire_time = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    
    # Tạo unique identifier cho refresh token
    jti = secrets.token_urlsafe(32)  # JWT ID - unique cho mỗi token
    
    payload = {
        "sub": str(user_id),
        "type": "refresh",             # Đánh dấu đây là refresh token
        "jti": jti,                   # JWT ID để tracking và revoke
        "iat": datetime.utcnow(),
        "exp": expire_time
    }
    
    # Sử dụng secret key riêng cho refresh token
    token = jwt.encode(payload, REFRESH_SECRET_KEY, algorithm=ALGORITHM)
    print(f"✅ Refresh token created for user {user_id} (expires in {REFRESH_TOKEN_EXPIRE_DAYS} days)")
    return token
```
**Giải thích:**
- `timedelta(days=7)`: Refresh token sống 7 ngày
- `secrets.token_urlsafe(32)`: Tạo JWT ID ngẫu nhiên để track token
- `type: "refresh"`: Phân biệt với access token
- Dùng `REFRESH_SECRET_KEY` riêng biệt

```python
def verify_access_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Access Token
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra loại token
        if payload.get("type") != "access":
            print("❌ Token is not an access token")
            return None
        
        # Kiểm tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            print("❌ Access token expired")
            return None
            
        return payload
        
    except JWTError as e:
        print(f"❌ Access token verification failed: {e}")
        return None
```
**Giải thích:**
- Chỉ verify access token với `SECRET_KEY`
- Kiểm tra `type == "access"` để tránh confusion attack
- Return None nếu token hết hạn hoặc không hợp lệ

```python
def verify_refresh_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Refresh Token
    """
    try:
        # Decode với refresh secret key
        payload = jwt.decode(token, REFRESH_SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra loại token
        if payload.get("type") != "refresh":
            print("❌ Token is not a refresh token")
            return None
        
        # Kiểm tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            print("❌ Refresh token expired")
            return None
            
        return payload
        
    except JWTError as e:
        print(f"❌ Refresh token verification failed: {e}")
        return None
```
**Giải thích:**
- Dùng `REFRESH_SECRET_KEY` để decode refresh token
- Kiểm tra `type == "refresh"`
- Nếu refresh token hết hạn → user phải đăng nhập lại

---

## 🔄 Bước 2: Implement Refresh Token Flow

### 2.1 Tạo file `app/core/refresh_service.py`

```python
from typing import Optional, Dict, Any, Tuple
from .jwt_utils import (
    create_access_token, 
    create_refresh_token, 
    verify_refresh_token
)

# Fake user database (production sẽ dùng real database)
FAKE_USERS = {
    1: {"username": "admin", "role": "admin"},
    2: {"username": "user1", "role": "user"},
    3: {"username": "user2", "role": "user"}
}

class RefreshTokenService:
    """
    Service để handle refresh token operations
    """
    
    def __init__(self):
        # In-memory storage cho active refresh tokens (production dùng Redis/Database)
        self.active_refresh_tokens = set()
        print("🔄 RefreshTokenService initialized")
    
    def create_token_pair(self, user_id: int, username: str, role: str) -> Dict[str, Any]:
        """
        Tạo cặp Access Token + Refresh Token
        
        Args:
            user_id: ID của user
            username: Tên user
            role: Vai trò user
        
        Returns:
            Dict: Chứa cả access_token và refresh_token
        """
        # Tạo access token
        access_token = create_access_token(user_id, username, role)
        
        # Tạo refresh token
        refresh_token = create_refresh_token(user_id)
        
        # Lưu refresh token vào active list
        self.active_refresh_tokens.add(refresh_token)
        
        print(f"✅ Token pair created for user {username}")
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": 1800,  # 30 minutes in seconds
            "refresh_expires_in": 604800  # 7 days in seconds
        }
```
**Giải thích:**
- `active_refresh_tokens`: Set lưu trữ refresh tokens đang hoạt động
- Production sẽ dùng Redis hoặc Database thay vì in-memory
- Return cả access và refresh token cùng lúc

```python
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """
        Tạo access token mới từ refresh token
        
        Args:
            refresh_token: Refresh token từ client
        
        Returns:
            Dict: Token pair mới hoặc None nếu refresh token không hợp lệ
        """
        print(f"🔄 Attempting to refresh token: {refresh_token[:20]}...")
        
        # Kiểm tra refresh token có trong active list không
        if refresh_token not in self.active_refresh_tokens:
            print("❌ Refresh token not found in active tokens")
            return None
        
        # Verify refresh token
        payload = verify_refresh_token(refresh_token)
        if not payload:
            # Xóa token không hợp lệ khỏi active list
            self.active_refresh_tokens.discard(refresh_token)
            return None
```
**Giải thích:**
- Kiểm tra refresh token có trong active list không
- Verify refresh token với secret key riêng
- Xóa token không hợp lệ khỏi active list

```python
        # Lấy user info từ refresh token
        user_id = int(payload.get("sub"))
        user_data = FAKE_USERS.get(user_id)
        
        if not user_data:
            print(f"❌ User {user_id} not found")
            self.active_refresh_tokens.discard(refresh_token)
            return None
        
        print(f"✅ Refresh token valid for user: {user_data['username']}")
```
**Giải thích:**
- Lấy user_id từ refresh token payload
- Query database để lấy thông tin user mới nhất
- Kiểm tra user vẫn tồn tại và active

```python
        # Tạo access token mới
        new_access_token = create_access_token(
            user_id=user_id,
            username=user_data["username"],
            role=user_data["role"]
        )
        
        # Optional: Refresh Token Rotation (tạo refresh token mới)
        new_refresh_token = create_refresh_token(user_id)
        
        # Xóa refresh token cũ và thêm token mới
        self.active_refresh_tokens.discard(refresh_token)
        self.active_refresh_tokens.add(new_refresh_token)
        
        print(f"✅ Tokens refreshed for user {user_data['username']}")
        
        return {
            "access_token": new_access_token,
            "refresh_token": new_refresh_token,  # Token mới
            "token_type": "bearer",
            "expires_in": 1800,
            "refresh_expires_in": 604800
        }
```
**Giải thích:**
- Tạo access token mới với thông tin user hiện tại
- **Refresh Token Rotation**: Tạo refresh token mới thay thế token cũ
- Xóa token cũ và thêm token mới vào active list
- Tăng bảo mật: Token cũ không thể sử dụng lại

```python
    def revoke_refresh_token(self, refresh_token: str) -> bool:
        """
        Thu hồi refresh token (dùng cho logout)
        
        Args:
            refresh_token: Token cần thu hồi
        
        Returns:
            bool: True nếu thu hồi thành công
        """
        if refresh_token in self.active_refresh_tokens:
            self.active_refresh_tokens.remove(refresh_token)
            print(f"✅ Refresh token revoked: {refresh_token[:20]}...")
            return True
        else:
            print(f"❌ Refresh token not found: {refresh_token[:20]}...")
            return False
    
    def get_active_tokens_count(self) -> int:
        """
        Lấy số lượng refresh tokens đang hoạt động
        """
        return len(self.active_refresh_tokens)
```

# Tạo singleton instance
refresh_service = RefreshTokenService()
```

---

## 🌐 Bước 3: Cập nhật API Endpoints

### 3.1 Cập nhật `app/api/auth_basic.py`

```python
from ..core.refresh_service import refresh_service
from pydantic import BaseModel

# Thêm Pydantic models
class RefreshTokenRequest(BaseModel):
    refresh_token: str

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    refresh_expires_in: int

# Cập nhật login endpoint
@router.post("/login", response_model=TokenResponse)
def login_with_refresh(login_data: LoginRequest) -> TokenResponse:
    """
    Đăng nhập và tạo cặp Access + Refresh Token
    """
    username = login_data.username
    password = login_data.password
    
    print(f"🔐 Login attempt for username: {username}")
    
    # Kiểm tra credentials (giống như trước)
    if username not in FAKE_USERS:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
    
    user_data = FAKE_USERS[username]
    if password != user_data["password"]:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
```
**Giải thích:**
- Cùng logic authentication như trước
- Nhưng sẽ return cả access và refresh token

```python
    # Tạo token pair thay vì chỉ access token
    try:
        token_pair = refresh_service.create_token_pair(
            user_id=user_data["user_id"],
            username=username,
            role=user_data["role"]
        )
        
        print(f"✅ Login successful for {username} with refresh token")
        
        return TokenResponse(**token_pair)
        
    except Exception as e:
        print(f"❌ Error during login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )
```
**Giải thích:**
- Gọi `refresh_service.create_token_pair()` thay vì chỉ tạo access token
- Return cả access_token và refresh_token
- Client sẽ lưu cả hai tokens

### 3.2 Refresh Token endpoint

```python
@router.post("/refresh", response_model=TokenResponse)
def refresh_token(refresh_request: RefreshTokenRequest) -> TokenResponse:
    """
    Refresh access token sử dụng refresh token
    
    Args:
        refresh_request: Chứa refresh token
    
    Returns:
        TokenResponse: Token pair mới
    """
    refresh_token = refresh_request.refresh_token
    
    print(f"🔄 Refresh token request received")
    
    # Refresh access token
    token_pair = refresh_service.refresh_access_token(refresh_token)
    
    if not token_pair:
        print("❌ Refresh token invalid or expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired refresh token. Please login again.",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    print("✅ Access token refreshed successfully")
    return TokenResponse(**token_pair)
```
**Giải thích:**
- Endpoint để client refresh access token
- Nhận refresh token từ request body
- Return token pair mới (với refresh token rotation)
- Nếu refresh token không hợp lệ → client phải đăng nhập lại

### 3.3 Token info endpoint

```python
@router.get("/token-info")
def get_token_info(request: Request):
    """
    Lấy thông tin về token hiện tại
    """
    current_user = request.state.current_user
    
    return {
        "message": "Token information",
        "user_info": current_user,
        "active_refresh_tokens": refresh_service.get_active_tokens_count(),
        "token_type": current_user["token_payload"].get("type"),
        "issued_at": current_user["token_payload"].get("iat"),
        "expires_at": current_user["token_payload"].get("exp")
    }
```

---

## 🧪 Bước 4: Testing Refresh Token Flow

### 4.1 Test complete flow

**1. Login để lấy token pair:**
```bash
curl -X POST "http://localhost:8001/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user1",
    "password": "user123"
  }'
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
  "token_type": "bearer",
  "expires_in": 1800,
  "refresh_expires_in": 604800
}
```

**2. Sử dụng access token:**
```bash
curl -X GET "http://localhost:8001/auth/me-middleware" \
  -H "Authorization: Bearer ACCESS_TOKEN_HERE"
```

**3. Khi access token hết hạn, refresh token:**
```bash
curl -X POST "http://localhost:8001/auth/refresh" \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "REFRESH_TOKEN_HERE"
  }'
```

**4. Sử dụng access token mới:**
```bash
curl -X GET "http://localhost:8001/auth/me-middleware" \
  -H "Authorization: Bearer NEW_ACCESS_TOKEN_HERE"
```

### 4.2 Client-side implementation example

```javascript
// JavaScript client example
class AuthClient {
    constructor() {
        this.accessToken = localStorage.getItem('access_token');
        this.refreshToken = localStorage.getItem('refresh_token');
    }
    
    async login(username, password) {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        // Lưu cả hai tokens
        this.accessToken = data.access_token;
        this.refreshToken = data.refresh_token;
        localStorage.setItem('access_token', this.accessToken);
        localStorage.setItem('refresh_token', this.refreshToken);
    }
    
    async makeRequest(url, options = {}) {
        // Thêm access token vào header
        options.headers = {
            ...options.headers,
            'Authorization': `Bearer ${this.accessToken}`
        };
        
        let response = await fetch(url, options);
        
        // Nếu access token hết hạn (401), thử refresh
        if (response.status === 401) {
            const refreshed = await this.refreshAccessToken();
            if (refreshed) {
                // Retry request với token mới
                options.headers['Authorization'] = `Bearer ${this.accessToken}`;
                response = await fetch(url, options);
            }
        }
        
        return response;
    }
    
    async refreshAccessToken() {
        try {
            const response = await fetch('/auth/refresh', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ refresh_token: this.refreshToken })
            });
            
            if (response.ok) {
                const data = await response.json();
                
                // Cập nhật tokens
                this.accessToken = data.access_token;
                this.refreshToken = data.refresh_token;
                localStorage.setItem('access_token', this.accessToken);
                localStorage.setItem('refresh_token', this.refreshToken);
                
                return true;
            }
        } catch (error) {
            console.error('Refresh token failed:', error);
        }
        
        // Refresh failed, redirect to login
        this.logout();
        return false;
    }
    
    logout() {
        this.accessToken = null;
        this.refreshToken = null;
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
    }
}
```

---

## 📝 Tóm tắt

### Refresh Token Flow:

```
1. Login → Access Token (30 min) + Refresh Token (7 days)
2. Use Access Token for API calls
3. Access Token expires → Use Refresh Token to get new Access Token
4. Continue with new Access Token
5. Refresh Token expires → User must login again
```

### Security Benefits:

- ✅ **Short-lived Access Tokens**: Giảm rủi ro khi bị đánh cắp
- ✅ **Token Rotation**: Refresh token mới sau mỗi lần refresh
- ✅ **Separate Secret Keys**: Access và Refresh dùng keys khác nhau
- ✅ **Token Tracking**: Có thể revoke refresh tokens
- ✅ **Better UX**: User không cần đăng nhập lại thường xuyên

### Files đã tạo/cập nhật:

- Updated `app/core/jwt_utils.py`: Thêm refresh token functions
- `app/core/refresh_service.py`: Service quản lý refresh tokens
- Updated `app/api/auth_basic.py`: Login và refresh endpoints

### Next steps:

- ✅ JWT cơ bản hoàn thành
- ✅ Middleware Authorization hoàn thành
- ✅ Refresh Token flow hoàn thành
- 🔄 Tiếp theo: Logout + Blacklist

---

*Refresh Token giúp cân bằng giữa bảo mật và user experience!*
