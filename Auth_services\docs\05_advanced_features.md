# 🚀 Advanced Features - Fingerprint, Metrics, Logging

## 📋 Mục tiêu
- ✅ Token Fingerprinting để chống token theft
- ✅ Metrics collection và monitoring
- ✅ Advanced logging và audit trail
- ✅ Security alerts và anomaly detection

---

## 🔐 Bước 1: Token Fingerprinting

### 1.1 Tạo file `app/core/fingerprint_service.py`

```python
import hashlib
from typing import Optional, Dict, Any
from fastapi import Request
import json

class TokenFingerprintService:
    """
    Service tạo và verify token fingerprints để chống token theft
    """
    
    def __init__(self):
        print("🔐 TokenFingerprintService initialized")
    
    def create_fingerprint(self, request: Request) -> str:
        """
        Tạo fingerprint từ client information
        
        Args:
            request: FastAPI request object
        
        Returns:
            str: Fingerprint hash
        """
        # Lấy thông tin client
        user_agent = request.headers.get("user-agent", "")
        ip_address = self._get_client_ip(request)
        accept_language = request.headers.get("accept-language", "")
        accept_encoding = request.headers.get("accept-encoding", "")
        
        # Tạo fingerprint data
        fingerprint_data = {
            "user_agent": user_agent,
            "ip_address": ip_address,
            "accept_language": accept_language,
            "accept_encoding": accept_encoding
        }
        
        # Hash fingerprint data
        data_string = json.dumps(fingerprint_data, sort_keys=True)
        fingerprint_hash = hashlib.sha256(data_string.encode()).hexdigest()[:16]
        
        print(f"🔐 Fingerprint created: {fingerprint_hash} for IP {ip_address}")
        return fingerprint_hash
```
**Giải thích:**
- Fingerprint = "dấu vân tay" của client dựa trên browser/device info
- Kết hợp User-Agent, IP, Accept headers để tạo unique identifier
- Hash SHA256 và lấy 16 ký tự đầu để compact
- Nếu token bị đánh cắp và dùng từ device khác → fingerprint sẽ khác

```python
    def verify_fingerprint(self, request: Request, expected_fingerprint: str) -> bool:
        """
        Verify fingerprint của request với expected fingerprint
        
        Args:
            request: FastAPI request object
            expected_fingerprint: Fingerprint từ token
        
        Returns:
            bool: True nếu fingerprint khớp
        """
        current_fingerprint = self.create_fingerprint(request)
        
        if current_fingerprint == expected_fingerprint:
            print(f"✅ Fingerprint verified: {current_fingerprint}")
            return True
        else:
            print(f"❌ Fingerprint mismatch! Expected: {expected_fingerprint}, Got: {current_fingerprint}")
            return False
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Lấy IP address của client (xử lý proxy/load balancer)
        """
        # Kiểm tra X-Forwarded-For header
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # Kiểm tra X-Real-IP header
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
    
    def get_client_info(self, request: Request) -> Dict[str, str]:
        """
        Lấy thông tin chi tiết về client (để logging/debugging)
        
        Args:
            request: FastAPI request object
        
        Returns:
            Dict: Client information
        """
        return {
            "ip_address": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent", ""),
            "accept_language": request.headers.get("accept-language", ""),
            "accept_encoding": request.headers.get("accept-encoding", ""),
            "host": request.headers.get("host", ""),
            "referer": request.headers.get("referer", ""),
            "fingerprint": self.create_fingerprint(request)
        }

# Singleton instance
fingerprint_service = TokenFingerprintService()
```

### 1.2 Cập nhật JWT Utils với Fingerprint

```python
# Cập nhật app/core/jwt_utils.py
from .fingerprint_service import fingerprint_service
from fastapi import Request

def create_access_token_with_fingerprint(
    user_id: int, 
    username: str, 
    role: str,
    request: Request
) -> str:
    """
    Tạo Access Token với fingerprint
    
    Args:
        user_id: ID của user
        username: Tên user
        role: Vai trò user
        request: FastAPI request để tạo fingerprint
    
    Returns:
        str: JWT token với fingerprint
    """
    expire_time = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    # Tạo fingerprint từ request
    fingerprint = fingerprint_service.create_fingerprint(request)
    
    payload = {
        "sub": str(user_id),
        "username": username,
        "role": role,
        "type": "access",
        "fp": fingerprint,           # Thêm fingerprint vào payload
        "iat": datetime.utcnow(),
        "exp": expire_time
    }
    
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    print(f"✅ Access token with fingerprint created for {username}")
    return token
```
**Giải thích:**
- Thêm fingerprint vào token payload
- Token chỉ hợp lệ khi sử dụng từ cùng device/browser
- Nếu token bị đánh cắp và dùng từ device khác → fingerprint không khớp

```python
def verify_access_token_with_fingerprint(token: str, request: Request) -> Optional[Dict[str, Any]]:
    """
    Verify Access Token với fingerprint check
    
    Args:
        token: JWT token string
        request: FastAPI request để verify fingerprint
    
    Returns:
        Dict: Token payload nếu hợp lệ, None nếu không hợp lệ
    """
    # Kiểm tra blacklist trước
    if blacklist_service.is_token_blacklisted(token):
        print("❌ Token is blacklisted")
        return None
    
    # Verify token
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra loại token
        if payload.get("type") != "access":
            print("❌ Token is not an access token")
            return None
        
        # Kiểm tra hết hạn
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            print("❌ Access token expired")
            return None
        
        # Verify fingerprint
        expected_fingerprint = payload.get("fp")
        if expected_fingerprint:
            if not fingerprint_service.verify_fingerprint(request, expected_fingerprint):
                print("❌ Token fingerprint verification failed - possible token theft")
                return None
        else:
            print("⚠️ Token has no fingerprint (legacy token)")
        
        print(f"✅ Access token with fingerprint verified for user: {payload.get('username')}")
        return payload
        
    except JWTError as e:
        print(f"❌ Access token verification failed: {e}")
        return None
```
**Giải thích:**
- Verify fingerprint sau khi verify JWT signature
- Nếu fingerprint không khớp → có thể token bị đánh cắp
- Legacy tokens (không có fingerprint) vẫn được accept với warning

---

## 📊 Bước 2: Metrics Collection

### 2.1 Tạo file `app/core/metrics_service.py`

```python
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, Any, List
import threading
import time

class AuthMetricsService:
    """
    Service thu thập và quản lý metrics về authentication
    """
    
    def __init__(self):
        self.metrics = {
            # Token metrics
            "tokens_created": defaultdict(int),
            "tokens_verified": defaultdict(int),
            "tokens_failed": defaultdict(int),
            "tokens_blacklisted": defaultdict(int),
            
            # User metrics
            "logins_successful": defaultdict(int),
            "logins_failed": defaultdict(int),
            "logouts": defaultdict(int),
            
            # Security metrics
            "fingerprint_mismatches": defaultdict(int),
            "suspicious_activities": defaultdict(int),
            
            # Performance metrics
            "response_times": deque(maxlen=1000),  # Keep last 1000 response times
        }
        
        # Time-based metrics (last 24 hours)
        self.hourly_metrics = defaultdict(lambda: defaultdict(int))
        
        # Thread lock for thread-safe operations
        self.lock = threading.Lock()
        
        print("📊 AuthMetricsService initialized")
    
    def record_token_created(self, token_type: str, user_id: int):
        """
        Record token creation
        
        Args:
            token_type: Type of token (access, refresh)
            user_id: ID của user
        """
        with self.lock:
            self.metrics["tokens_created"][token_type] += 1
            self._record_hourly_metric("tokens_created", token_type)
            
        print(f"📊 Recorded token creation: {token_type} for user {user_id}")
```
**Giải thích:**
- `defaultdict(int)`: Tự động tạo key với value = 0
- `deque(maxlen=1000)`: Queue với size limit để lưu response times
- `threading.Lock()`: Thread-safe cho concurrent requests
- Metrics được chia theo loại: token, user, security, performance

```python
    def record_token_verified(self, token_type: str, user_id: int, success: bool):
        """
        Record token verification
        
        Args:
            token_type: Type of token
            user_id: ID của user
            success: Verification thành công hay không
        """
        with self.lock:
            if success:
                self.metrics["tokens_verified"][token_type] += 1
                self._record_hourly_metric("tokens_verified", token_type)
            else:
                self.metrics["tokens_failed"][token_type] += 1
                self._record_hourly_metric("tokens_failed", token_type)
        
        status = "success" if success else "failed"
        print(f"📊 Recorded token verification: {token_type} {status} for user {user_id}")
    
    def record_login_attempt(self, username: str, success: bool, ip_address: str):
        """
        Record login attempt
        
        Args:
            username: Username
            success: Login thành công hay không
            ip_address: IP address của client
        """
        with self.lock:
            if success:
                self.metrics["logins_successful"]["total"] += 1
                self._record_hourly_metric("logins_successful", "total")
            else:
                self.metrics["logins_failed"]["total"] += 1
                self._record_hourly_metric("logins_failed", "total")
                
                # Track failed attempts per IP
                self.metrics["logins_failed"][f"ip_{ip_address}"] += 1
        
        status = "successful" if success else "failed"
        print(f"📊 Recorded login attempt: {username} {status} from {ip_address}")
    
    def record_fingerprint_mismatch(self, user_id: int, ip_address: str):
        """
        Record fingerprint mismatch (possible token theft)
        
        Args:
            user_id: ID của user
            ip_address: IP address của request
        """
        with self.lock:
            self.metrics["fingerprint_mismatches"]["total"] += 1
            self.metrics["fingerprint_mismatches"][f"user_{user_id}"] += 1
            self.metrics["suspicious_activities"][f"ip_{ip_address}"] += 1
            self._record_hourly_metric("fingerprint_mismatches", "total")
        
        print(f"🚨 Recorded fingerprint mismatch: user {user_id} from {ip_address}")
    
    def record_response_time(self, endpoint: str, response_time: float):
        """
        Record API response time
        
        Args:
            endpoint: API endpoint
            response_time: Response time in seconds
        """
        with self.lock:
            self.metrics["response_times"].append({
                "endpoint": endpoint,
                "time": response_time,
                "timestamp": datetime.utcnow().isoformat()
            })
        
        print(f"📊 Recorded response time: {endpoint} took {response_time:.4f}s")
```

```python
    def get_metrics_summary(self) -> Dict[str, Any]:
        """
        Lấy tóm tắt metrics
        
        Returns:
            Dict: Metrics summary
        """
        with self.lock:
            # Calculate average response time
            if self.metrics["response_times"]:
                avg_response_time = sum(
                    item["time"] for item in self.metrics["response_times"]
                ) / len(self.metrics["response_times"])
            else:
                avg_response_time = 0
            
            return {
                "tokens": {
                    "created": dict(self.metrics["tokens_created"]),
                    "verified": dict(self.metrics["tokens_verified"]),
                    "failed": dict(self.metrics["tokens_failed"]),
                    "blacklisted": dict(self.metrics["tokens_blacklisted"])
                },
                "authentication": {
                    "successful_logins": dict(self.metrics["logins_successful"]),
                    "failed_logins": dict(self.metrics["logins_failed"]),
                    "logouts": dict(self.metrics["logouts"])
                },
                "security": {
                    "fingerprint_mismatches": dict(self.metrics["fingerprint_mismatches"]),
                    "suspicious_activities": dict(self.metrics["suspicious_activities"])
                },
                "performance": {
                    "average_response_time": f"{avg_response_time:.4f}s",
                    "total_requests": len(self.metrics["response_times"])
                },
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def get_security_alerts(self) -> List[Dict[str, Any]]:
        """
        Lấy danh sách security alerts
        
        Returns:
            List: Security alerts
        """
        alerts = []
        
        with self.lock:
            # Alert: Too many failed logins from same IP
            for key, count in self.metrics["logins_failed"].items():
                if key.startswith("ip_") and count > 5:  # Threshold: 5 failed attempts
                    ip = key.replace("ip_", "")
                    alerts.append({
                        "type": "brute_force_attempt",
                        "message": f"Multiple failed login attempts from IP {ip}",
                        "count": count,
                        "severity": "high"
                    })
            
            # Alert: Fingerprint mismatches
            for key, count in self.metrics["fingerprint_mismatches"].items():
                if key.startswith("user_") and count > 2:  # Threshold: 2 mismatches
                    user_id = key.replace("user_", "")
                    alerts.append({
                        "type": "possible_token_theft",
                        "message": f"Multiple fingerprint mismatches for user {user_id}",
                        "count": count,
                        "severity": "critical"
                    })
        
        return alerts
    
    def _record_hourly_metric(self, metric_type: str, key: str):
        """
        Record metric theo giờ (để trending analysis)
        """
        current_hour = datetime.utcnow().strftime("%Y-%m-%d-%H")
        self.hourly_metrics[current_hour][f"{metric_type}_{key}"] += 1

# Singleton instance
metrics_service = AuthMetricsService()
```

---

## 📝 Bước 3: Advanced Logging

### 3.1 Tạo file `app/core/audit_logger.py`

```python
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum

class AuditEventType(Enum):
    """
    Enum cho các loại audit events
    """
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGOUT = "logout"
    TOKEN_CREATED = "token_created"
    TOKEN_VERIFIED = "token_verified"
    TOKEN_BLACKLISTED = "token_blacklisted"
    FINGERPRINT_MISMATCH = "fingerprint_mismatch"
    PERMISSION_DENIED = "permission_denied"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"

class AuditLogger:
    """
    Advanced audit logger cho security events
    """
    
    def __init__(self, log_file: str = "auth_audit.log"):
        """
        Khởi tạo audit logger
        
        Args:
            log_file: Path to audit log file
        """
        # Setup logger
        self.logger = logging.getLogger("auth_audit")
        self.logger.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)  # Only warnings+ to console
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        if not self.logger.handlers:  # Avoid duplicate handlers
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
        
        print(f"📝 AuditLogger initialized, logging to: {log_file}")
```
**Giải thích:**
- Separate logger cho audit events
- File handler: Log tất cả events vào file
- Console handler: Chỉ log warnings+ ra console
- JSON format để dễ parse và analyze

```python
    def log_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        severity: str = "info"
    ):
        """
        Log audit event
        
        Args:
            event_type: Loại event
            user_id: ID của user (nếu có)
            username: Username (nếu có)
            ip_address: IP address của client
            user_agent: User agent string
            details: Chi tiết bổ sung
            severity: Mức độ nghiêm trọng (info, warning, error, critical)
        """
        # Tạo audit record
        audit_record = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type.value,
            "user_id": user_id,
            "username": username,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "details": details or {},
            "severity": severity
        }
        
        # Convert to JSON string
        log_message = json.dumps(audit_record, ensure_ascii=False)
        
        # Log với level phù hợp
        if severity == "critical":
            self.logger.critical(log_message)
        elif severity == "error":
            self.logger.error(log_message)
        elif severity == "warning":
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def log_login_success(self, user_id: int, username: str, ip_address: str, user_agent: str):
        """Log successful login"""
        self.log_event(
            AuditEventType.LOGIN_SUCCESS,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            details={"login_method": "password"}
        )
    
    def log_login_failed(self, username: str, ip_address: str, user_agent: str, reason: str):
        """Log failed login"""
        self.log_event(
            AuditEventType.LOGIN_FAILED,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            details={"failure_reason": reason},
            severity="warning"
        )
    
    def log_fingerprint_mismatch(
        self, 
        user_id: int, 
        username: str, 
        ip_address: str, 
        expected_fp: str, 
        actual_fp: str
    ):
        """Log fingerprint mismatch (possible token theft)"""
        self.log_event(
            AuditEventType.FINGERPRINT_MISMATCH,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            details={
                "expected_fingerprint": expected_fp,
                "actual_fingerprint": actual_fp,
                "possible_token_theft": True
            },
            severity="critical"
        )
    
    def log_permission_denied(self, user_id: int, username: str, resource: str, ip_address: str):
        """Log permission denied"""
        self.log_event(
            AuditEventType.PERMISSION_DENIED,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            details={"requested_resource": resource},
            severity="warning"
        )

# Singleton instance
audit_logger = AuditLogger()
```

---

## 🔄 Bước 4: Cập nhật Services với Advanced Features

### 4.1 Cập nhật Authentication Middleware

```python
# Cập nhật app/middleware/auth_middleware.py
from ..core.fingerprint_service import fingerprint_service
from ..core.metrics_service import metrics_service
from ..core.audit_logger import audit_logger, AuditEventType
from ..core.jwt_utils import verify_access_token_with_fingerprint

class AuthenticationMiddleware(BaseHTTPMiddleware):
    # ... (code trước giữ nguyên)
    
    async def dispatch(self, request: Request, call_next: Callable):
        start_time = time.time()
        
        # Log request
        client_info = fingerprint_service.get_client_info(request)
        print(f"🌐 {request.method} {request.url.path} from {client_info['ip_address']}")
        
        # Kiểm tra excluded paths
        if self._is_excluded_path(request.url.path):
            response = await call_next(request)
            self._record_metrics(request.url.path, start_time)
            return response
        
        # Lấy Authorization header
        authorization = request.headers.get("Authorization")
        if not authorization:
            audit_logger.log_event(
                AuditEventType.PERMISSION_DENIED,
                ip_address=client_info["ip_address"],
                user_agent=client_info["user_agent"],
                details={"reason": "missing_auth_header", "path": request.url.path}
            )
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Authorization header is required"}
            )
        
        # Parse token
        try:
            scheme, token = authorization.split(" ", 1)
            if scheme.lower() != "bearer":
                raise ValueError("Invalid scheme")
        except ValueError:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid authorization header format"}
            )
        
        # Verify token với fingerprint
        payload = verify_access_token_with_fingerprint(token, request)
        if payload is None:
            # Record failed verification
            metrics_service.record_token_verified("access", 0, False)
            
            audit_logger.log_event(
                AuditEventType.TOKEN_VERIFIED,
                ip_address=client_info["ip_address"],
                user_agent=client_info["user_agent"],
                details={"success": False, "path": request.url.path},
                severity="warning"
            )
            
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid, expired, or revoked token"}
            )
        
        # Success - record metrics và audit
        user_id = int(payload.get("sub", 0))
        username = payload.get("username")
        
        metrics_service.record_token_verified("access", user_id, True)
        
        # Lưu user info vào request state
        request.state.current_user = {
            "user_id": user_id,
            "username": username,
            "role": payload.get("role"),
            "token_payload": payload,
            "client_info": client_info
        }
        
        # Continue processing
        try:
            response = await call_next(request)
            self._record_metrics(request.url.path, start_time)
            return response
        except Exception as e:
            audit_logger.log_event(
                AuditEventType.SUSPICIOUS_ACTIVITY,
                user_id=user_id,
                username=username,
                ip_address=client_info["ip_address"],
                details={"error": str(e), "path": request.url.path},
                severity="error"
            )
            raise
    
    def _record_metrics(self, endpoint: str, start_time: float):
        """Record response time metrics"""
        response_time = time.time() - start_time
        metrics_service.record_response_time(endpoint, response_time)
```

### 4.2 Admin Endpoints cho Monitoring

```python
# Thêm vào app/api/auth_basic.py

@router.get("/admin/metrics")
def get_metrics(request: Request):
    """
    Admin endpoint: Lấy metrics summary
    """
    current_user = request.state.current_user
    
    if current_user["role"] != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    
    metrics_summary = metrics_service.get_metrics_summary()
    security_alerts = metrics_service.get_security_alerts()
    
    return {
        "metrics": metrics_summary,
        "security_alerts": security_alerts,
        "admin": current_user["username"]
    }

@router.get("/admin/security-alerts")
def get_security_alerts(request: Request):
    """
    Admin endpoint: Lấy security alerts
    """
    current_user = request.state.current_user
    
    if current_user["role"] != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    
    alerts = metrics_service.get_security_alerts()
    
    return {
        "alerts": alerts,
        "total_alerts": len(alerts),
        "critical_alerts": len([a for a in alerts if a["severity"] == "critical"]),
        "admin": current_user["username"]
    }
```

---

## 🧪 Bước 5: Testing Advanced Features

### 5.1 Test Fingerprint Protection

**1. Login từ browser A:**
```bash
curl -X POST "http://localhost:8001/auth/login" \
  -H "Content-Type: application/json" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
  -d '{"username": "user1", "password": "user123"}'
```

**2. Sử dụng token từ browser A:**
```bash
curl -X GET "http://localhost:8001/auth/me-middleware" \
  -H "Authorization: Bearer TOKEN" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
```
**Expected:** ✅ Success

**3. Sử dụng token từ browser B (khác User-Agent):**
```bash
curl -X GET "http://localhost:8001/auth/me-middleware" \
  -H "Authorization: Bearer TOKEN" \
  -H "User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
```
**Expected:** ❌ 401 Unauthorized (fingerprint mismatch)

### 5.2 Test Metrics Collection

**1. Thực hiện nhiều requests**
**2. Check metrics:**
```bash
curl -X GET "http://localhost:8001/auth/admin/metrics" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

**3. Check security alerts:**
```bash
curl -X GET "http://localhost:8001/auth/admin/security-alerts" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 5.3 Check Audit Logs

**File: `auth_audit.log`**
```json
{"timestamp": "2024-01-15T10:30:45.123456", "event_type": "login_success", "user_id": 1, "username": "admin", "ip_address": "127.0.0.1", "user_agent": "curl/7.68.0", "details": {"login_method": "password"}, "severity": "info"}

{"timestamp": "2024-01-15T10:31:12.789012", "event_type": "fingerprint_mismatch", "user_id": 2, "username": "user1", "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0", "details": {"expected_fingerprint": "abc123", "actual_fingerprint": "def456", "possible_token_theft": true}, "severity": "critical"}
```

---

## 📝 Tóm tắt

### Advanced Security Features:

- ✅ **Token Fingerprinting**: Bind tokens to specific devices/browsers
- ✅ **Comprehensive Metrics**: Track all authentication activities
- ✅ **Advanced Audit Logging**: Detailed security event logging
- ✅ **Security Alerts**: Automatic detection of suspicious activities
- ✅ **Performance Monitoring**: Response time tracking

### Security Benefits:

- 🛡️ **Token Theft Protection**: Fingerprinting prevents stolen token usage
- 📊 **Visibility**: Complete visibility into authentication activities
- 🚨 **Early Detection**: Automatic alerts for security incidents
- 📈 **Performance Insights**: Monitor API performance
- 🔍 **Forensics**: Detailed audit trail for security investigations

### Files đã tạo:

- `app/core/fingerprint_service.py`: Token fingerprinting service
- `app/core/metrics_service.py`: Metrics collection service
- `app/core/audit_logger.py`: Advanced audit logging
- Updated middleware và endpoints với advanced features

### Production Deployment:

- ✅ **Log Aggregation**: ELK Stack (Elasticsearch, Logstash, Kibana)
- ✅ **Metrics Monitoring**: Prometheus + Grafana
- ✅ **Alerting**: PagerDuty, Slack notifications
- ✅ **SIEM Integration**: Security Information and Event Management

---

## 🎯 Hoàn thành toàn bộ JWT Authentication System!

### Tất cả 5 phần đã hoàn thành:

1. ✅ **JWT Cơ bản** - Tạo + verify + bảo vệ routes
2. ✅ **Middleware Authorization** - Tự động check token
3. ✅ **Refresh Token Flow** - Tránh login lại liên tục
4. ✅ **Logout + Blacklist** - Thu hồi tokens
5. ✅ **Advanced Features** - Fingerprint, metrics, logging

### System Architecture:

```
Client Request
     ↓
CORS Middleware
     ↓
Auth Middleware (Token + Fingerprint Check)
     ↓
Role Middleware (Permission Check)
     ↓
Endpoint Handler
     ↓
Metrics Collection + Audit Logging
     ↓
Response
```

**Bạn đã có một JWT Authentication System production-ready hoàn chỉnh!** 🚀
