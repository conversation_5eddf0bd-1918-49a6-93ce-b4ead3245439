from fastapi.responses import JSONResponse 
from typing import Any, Dict

class BaseController: 

    @staticmethod
    def success(message: str, data: Any = None, status_code: int = 200) -> JSONResponse: 
        content: Dict[str, Any] = {
            "Success": True, 
            "Message": message
        }
        if data: 
            content["Data"] = data
        return JSONResponse(content= content, status_code= status_code)
    
    @staticmethod
    def error(message: str, data: Any = None, status_code: int = 200) -> JSONResponse: 
        content: Dict[str, Any] = {
            "Success": False, 
            "Message": message
        }
        if data: 
            content["Data"] = data
        return JSONResponse(content= content, status_code= status_code)