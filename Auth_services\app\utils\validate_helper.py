import re 
from typing import Optional

class ValidateHelper: 
    email_regex = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")
    username_regex = re.compile(r"^[a-zA-Z0-9_.-]{3,20}$")

    @classmethod 
    def validate_email(cls, email:str ) -> bool: 
        return bool(cls.email_regex.match(email))
    
    @classmethod
    def validate_username(cls, username: str) -> bool: 
        return bool(cls.username_regex.match(username))

    @classmethod
    def validate_password(cls, password: str) -> Optional[str]: 

        if len(password) < 8: 
            return "mật khẩu không thể nhỏ hơn 8 ký tự"
        
        if not any(c.isupper() for c in password):
            return "mật khẩu phải chứa ít nhất một ký tự viết hoa"
        
        if not any(c.islower() for c in password): 
            return "mật khẩu phải chứa ít nhất một ký tự viết thường"
        
        if not any(c.isdigit() for c in password): 
            return "mật khẩu phải chứa ít nhất một ký tự số"
        
        if not any(c in "!@#$%^&*()-_=+[]{};:,.<>/?\\|" for c in password):
            return "mật khẩu phải chứa ít nhất một ký tự tự đặc biệt"
        
        return None 