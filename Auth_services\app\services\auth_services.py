from sqlalchemy import text
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from typing import Dict, Any
from datetime import datetime
from ..core.security.password import HashedPassword
from ..utils.validate_helper import ValidateHelper
import logging

logger = logging.getLogger(__name__)

class AuthService:
    def __init__(self, db: Session, password_hashed: HashedPassword, validate_helper: ValidateHelper):
        self.db = db
        self.password_hashed = password_hashed 
        self.validate_helper = validate_helper

    def register(self, username: str, password: str) -> Dict[str, Any]:

        try:
            if not self.validate_helper.validate_username(username= username): 
                raise ValueError("UserName không hợp lệ")
            
            pw_error = self.validate_helper.validate_password(password= password)
            if pw_error: 
                raise ValueError("<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> không hợp lệ")


            hash_password = self.password_hashed.hash_password(password)
            # 3. <PERSON><PERSON><PERSON> bị data
            current_time = datetime.now()
            insert_data: Dict[str, Any] = {
                "username": username.strip(),
                "hash_password": hash_password,
                "active": True,
                "created_date": current_time,
                "updated_date": current_time
            }

            # 4. Thực hiện insert
            insert_sql = text("""
                INSERT INTO users (Username, HashPassword, Active, CreatedDate, UpdatedDate)
                VALUES (:username, :hash_password, :active, :created_date, :updated_date)
                RETURNING *
            """)

            result = self.db.execute(insert_sql, insert_data).mappings().first()

            if not result:
                raise RuntimeError("Không thể tạo user - không có kết quả trả về")

            # 5. Commit transaction
            self.db.commit()

            logger.info(f"Đã tạo user thành công: {username}")
            return dict(result)

        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"Lỗi integrity constraint: {e}")
            raise RuntimeError(f"Không thể tạo user '{username}' - vi phạm ràng buộc database")

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Lỗi database khi tạo user: {e}")
            raise RuntimeError(f"Lỗi database: {str(e)}")

        except Exception as e:
            self.db.rollback()
            logger.error(f"Lỗi không xác định khi tạo user: {e}")
            raise RuntimeError(f"Lỗi không xác định: {str(e)}")


    def login(self, username: str, password: str) -> Dict[str, Any]:
        try: 

            if not self.validate_helper.validate_username(username= username): 
                raise ValueError("UserName không hợp lệ")
            select_sql = text("SELECT * FROM users WHERE UserName = :username")
            params = {
                "username": username, 
            }             
            result = self.db.execute(select_sql, params).mappings().first()

            if not result: 
                raise RuntimeError("Tên đăng nhập hoặc mật khẩu không đúng")
            
            stored_hash = result["HashPassword"]
            if not self.password_hashed.verify_password(password, stored_hash):
                raise RuntimeError("Tên đăng nhập hoặc mật khẩu không đúng")

            user_data = dict(result)
            user_data.pop("HashPassword", None)

            return user_data

        except Exception as e:
            logger.error(f"Lỗi đăng nhập: {e}")
            raise RuntimeError(f"Lỗi đăng nhập: {str(e)}")