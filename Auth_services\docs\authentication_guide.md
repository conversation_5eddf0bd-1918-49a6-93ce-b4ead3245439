# 🔐 Hướng dẫn Authentication & Authorization cho FastAPI

## 📋 Mục lục
1. [Kh<PERSON><PERSON> niệm cơ bản](#khái-niệm-cơ-bản)
2. [JWT Token](#jwt-token)
3. [Middleware](#middleware)
4. [Authorization](#authorization)
5. [Thực hành từng bước](#thực-hành-từng-bước)

---

## 🎯 Khái niệm cơ bản

### Authentication vs Authorization

**🔑 Authentication (Xác thực):**
- **Là gì:** <PERSON><PERSON><PERSON> minh "bạn là ai?"
- **Ví dụ:** Đăng nhập bằng username/password
- **K<PERSON>t quả:** Xác định danh tính người dùng

**🛡️ Authorization (Phân quyền):**
- **Là gì:** <PERSON><PERSON><PERSON> minh "bạn có quyền làm gì?"
- **Ví dụ:** <PERSON><PERSON> có thể xóa user, User thường chỉ xem được profile
- **<PERSON><PERSON><PERSON> quả:** <PERSON><PERSON><PERSON> soát quyền truy cập tài nguyên

### Luồng hoạt động cơ bản:
```
1. User gửi username/password → Authentication
2. Server xác thực → Tạo JWT Token
3. User gửi token với mỗi request → Authorization
4. Server kiểm tra token → Cho phép/Từ chối truy cập
```

---

## 🎫 JWT Token (JSON Web Token)

### JWT là gì?
JWT là một chuẩn mở (RFC 7519) để truyền thông tin an toàn giữa các bên dưới dạng JSON object.

### Cấu trúc JWT:
```
Header.Payload.Signature
```

**Ví dụ JWT:**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

### Các phần của JWT:

**1. Header (Phần đầu):**
```json
{
  "alg": "HS256",    // Thuật toán mã hóa
  "typ": "JWT"       // Loại token
}
```

**2. Payload (Dữ liệu):**
```json
{
  "sub": "1234567890",           // Subject (ID người dùng)
  "name": "John Doe",            // Tên người dùng
  "role": "admin",               // Vai trò
  "iat": 1516239022,             // Issued at (thời gian tạo)
  "exp": 1516242622              // Expiration (thời gian hết hạn)
}
```

**3. Signature (Chữ ký):**
```
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret
)
```

### Ưu điểm JWT:
- ✅ **Stateless:** Server không cần lưu trữ session
- ✅ **Portable:** Có thể sử dụng across domains
- ✅ **Self-contained:** Chứa tất cả thông tin cần thiết
- ✅ **Secure:** Có chữ ký để verify tính toàn vẹn

### Nhược điểm JWT:
- ❌ **Size:** Lớn hơn session ID
- ❌ **Revocation:** Khó thu hồi token trước khi hết hạn
- ❌ **Storage:** Cần bảo mật nơi lưu trữ (client-side)

---

## 🔧 Middleware

### Middleware là gì?
Middleware là các function chạy **trước** và **sau** mỗi request, cho phép:
- Xử lý request trước khi đến endpoint
- Xử lý response trước khi trả về client
- Thêm logic chung cho toàn bộ application

### Luồng hoạt động Middleware:
```
Request → Middleware 1 → Middleware 2 → Endpoint → Middleware 2 → Middleware 1 → Response
```

### Các loại Middleware phổ biến:

**1. Authentication Middleware:**
- Kiểm tra token trong header
- Xác thực người dùng
- Thêm user info vào request

**2. Authorization Middleware:**
- Kiểm tra quyền truy cập
- Verify role/permissions
- Block unauthorized requests

**3. Logging Middleware:**
- Log request/response
- Monitor performance
- Debug application

**4. CORS Middleware:**
- Handle Cross-Origin requests
- Set CORS headers
- Security configuration

---

## 🛡️ Authorization

### Các mô hình phân quyền:

**1. Role-Based Access Control (RBAC):**
```
User → Role → Permissions
```
- User có Role (admin, user, moderator)
- Role có Permissions (read, write, delete)

**2. Permission-Based:**
```
User → Permissions (trực tiếp)
```
- User có permissions cụ thể
- Linh hoạt hơn nhưng phức tạp quản lý

**3. Resource-Based:**
```
User → Resource → Actions
```
- Kiểm tra quyền trên từng resource cụ thể
- Ví dụ: User chỉ edit được post của mình

### Cách implement Authorization:

**1. Decorator Pattern:**
```python
@require_role("admin")
def delete_user():
    pass
```

**2. Dependency Injection:**
```python
def get_admin_user(current_user: User = Depends(get_current_user)):
    if current_user.role != "admin":
        raise HTTPException(403)
    return current_user
```

**3. Middleware Pattern:**
```python
# Check permissions trong middleware
if not has_permission(user, resource, action):
    raise HTTPException(403)
```

---

## 🚀 Thực hành từng bước

### Bước 1: Cài đặt dependencies
```bash
pip install python-jose[cryptography] passlib[bcrypt] python-multipart
```

### Bước 2: Tạo JWT utilities
```python
# app/core/security.py
from datetime import datetime, timedelta
from jose import JWTError, jwt
from passlib.context import CryptContext

SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None
```

### Bước 3: Tạo Authentication dependency
```python
# app/core/deps.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    # Lấy user từ database
    user = get_user_by_id(user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    return user
```

### Bước 4: Tạo Authorization decorators
```python
# app/core/permissions.py
from functools import wraps
from fastapi import HTTPException, status

def require_role(required_role: str):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user or current_user.role != required_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions"
                )
            return func(*args, **kwargs)
        return wrapper
    return decorator
```

---

## 📚 Tài liệu tham khảo

- [FastAPI Security Documentation](https://fastapi.tiangolo.com/tutorial/security/)
- [JWT.io](https://jwt.io/) - JWT Debugger
- [OAuth 2.0 RFC](https://tools.ietf.org/html/rfc6749)
- [JSON Web Token RFC](https://tools.ietf.org/html/rfc7519)

---

## 🎯 Bài tập thực hành

1. **Tạo login endpoint** trả về JWT token
2. **Implement middleware** để verify token
3. **Tạo protected routes** với different roles
4. **Add refresh token** mechanism
5. **Implement logout** functionality

---

*Tài liệu này sẽ được mở rộng với các ví dụ code chi tiết trong phần tiếp theo...*
