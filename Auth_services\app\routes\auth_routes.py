from fastapi import Depends, APIRouter
from ..controllers.auth_controllers import AuthController
from ..schemas.auth import RegisterRequest, UserResponse
 
router = APIRouter(prefix="/auth", tags =["Auth"])

@router.post("/register", response_model= UserResponse)
# def register(username: str, password: str, controller: AuthController = Depends()): 
def register(payload: RegisterRequest, controller: AuthController = Depends()): 
    # return authcontroller.register(username= username, password= password) 
    return controller.register(payload) 

 