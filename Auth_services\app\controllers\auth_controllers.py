from fastapi import Depends
from ..core.database import get_db
from ..services.auth_services import AuthService
from ..core.security.password import HashedPassword
from ..utils.validate_helper import ValidateHelper
from .base_controller import BaseController
from ..schemas.auth import RegisterRequest, LoginRequest
from sqlalchemy.orm import Session

class AuthController(BaseController): 

    def __init__(self, db: Session = Depends(get_db)): 
        self.db = db 
        self.auth_serives = AuthService(
            db = self.db, 
            password_hashed= HashedPassword(), 
            validate_helper= ValidateHelper(), 
        )

    def register(self, payload: RegisterRequest):
        try:
            # Lấy username và password từ payload
            user = self.auth_serives.register(username=payload.username, password=payload.password)
            return self.success(message="đăng ký thành công", data=user, status_code=201)
        except Exception as e:
           return self.error(message=f"Lỗi: {e}", status_code=400)
    
    def login(self, payload: LoginRequest): 
        try: 
            user = self.auth_serives.login(username= payload.username, password= payload.password)
            return self.success(message="đăng nhập thành công", data=user, status_code=201)
        except Exception as e: 
            return self.error(message=f"Lỗi: {e}", status_code=400)