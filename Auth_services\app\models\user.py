from ..core.database import Base
from sqlalchemy import Column, Integer, String, Boolean, DateTime, func

class User(Base):

    __tablename__ = "users"
    UserID = Column(Integer, primary_key=True, autoincrement=True)
    UserName = Column(String(50), nullable=False, unique=True)
    HashPassword = Column(String(255), nullable=False)
    Active = Column(Boolean, default=True)
    CreatedDate = Column(DateTime, default=func.now())
    UpdatedDate = Column(DateTime, default=func.now(), onupdate=func.now())
